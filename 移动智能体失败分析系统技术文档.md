# 移动智能体失败分析系统技术文档

## 1. 系统概述

### 1.1 项目背景
移动智能体失败分析系统是一个基于CAGUI数据集的智能体评估分析工具，专门用于深度分析任务完成度(Su)为0的失败案例，通过豆包大语言模型提供专业的失败原因分析和改进建议。

### 1.2 核心功能
- **失败案例自动识别**: 从评估结果中筛选Su=0的完全失败案例
- **多维度数据整合**: 整合智能体轨迹、人类基准和评估指标
- **AI驱动分析**: 使用豆包模型进行深度失败原因分析
- **结构化输出**: 生成标准化的分析报告和JSON格式结果

### 1.3 技术架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   数据加载层    │    │   分析处理层    │    │   输出展示层    │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ • 评估结果      │    │ • 失败筛选      │    │ • JSON报告      │
│ • 智能体轨迹    │───▶│ • 数据整合      │───▶│ • 控制台输出    │
│ • 人类基准      │    │ • AI分析        │    │ • 日志记录      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                    ┌─────────────────┐
                    │   豆包AI模型    │
                    │ (Doubao-Seed)   │
                    └─────────────────┘
```

## 2. 系统设计

### 2.1 数据模型

#### 2.1.1 输入数据源
```python
# 评估结果数据 (multiagent_uitars_results.csv)
{
    "instruction": "任务指令",
    "human_id": "人类基准ID", 
    "agent_id": "智能体ID",
    "task_type": "任务难度等级",
    "app_category": "应用分类",
    "human_steps": "人类步骤数",
    "agent_steps": "智能体步骤数", 
    "Su": "任务完成度",
    "PS": "过程正确性",
    "RE": "相对效率", 
    "CR": "策略一致性",
    "evaluate_reason": "评估理由"
}

# 智能体轨迹数据 (mul-uitar-trace.csv)
{
    "id": "智能体ID",
    "instruction": "任务指令",
    "actions": "执行动作序列",
    "ai_summaries": "AI执行总结",
    "is_success": "执行成功标志"
}

# 人类基准数据 (gt.csv)
{
    "id": "人类基准ID",
    "instruction": "任务指令", 
    "action_trace": "人类操作轨迹",
    "human_steps": "人类步骤数",
    "expected_result": "预期结果"
}
```

#### 2.1.2 输出数据结构
```python
# 分析结果结构
{
    "agent_id": "智能体ID",
    "human_id": "人类基准ID", 
    "instruction": "任务指令",
    "analysis": "豆包模型分析结果"
}
```

### 2.2 核心算法流程

#### 2.2.1 失败案例筛选算法
```python
def get_failed_records(results_df: pd.DataFrame) -> pd.DataFrame:
    """
    筛选逻辑：Su == 0.0
    返回：完全失败的案例集合
    """
    failed_records = results_df[results_df['Su'] == 0.0].copy()
    return failed_records
```

#### 2.2.2 数据整合算法
```python
# 多表关联逻辑
agent_record = agent_df[agent_df['id'] == row['agent_id']].iloc[0]
gt_record = gt_df[gt_df['id'] == row['human_id']].iloc[0]

# 指标整合
metrics = {
    'Su': row['Su'],    # 任务完成度
    'PS': row['PS'],    # 过程正确性  
    'RE': row['RE'],    # 相对效率
    'CR': row['CR']     # 策略一致性
}
```

## 3. AI分析模块

### 3.1 豆包模型集成

#### 3.1.1 模型配置
```python
# 环境配置
DOUBAO_API_KEY = os.getenv("ARK_API_KEY")
DOUBAO_BASE_URL = os.getenv("ARK_API_BASE") 
DOUBAO_MODEL = os.getenv("ARK_MODEL")  # doubao-seed-1-6-250615

# 客户端初始化
client = Ark(
    api_key=DOUBAO_API_KEY,
    base_url=DOUBAO_BASE_URL
)
```

#### 3.1.2 Prompt工程设计
系统采用结构化Prompt设计，包含以下关键组件：

**角色定义**:
```
你是一个专业的移动智能体评估分析师，擅长分析智能体任务执行失败的原因。
```

**分析维度**:
1. **任务完成度分析 (Su=0的原因)**
2. **过程正确性分析 (PS)**  
3. **效率对比分析 (RE)**
4. **策略一致性分析 (CR)**

**输入数据结构**:
- 任务指令
- 智能体执行轨迹
- 智能体总结
- 人类基准轨迹
- 评估指标

### 3.2 分析质量保证

#### 3.2.1 参数优化
```python
response = client.chat.completions.create(
    model=DOUBAO_MODEL,
    messages=[...],
    temperature=0.1,      # 低温度确保分析稳定性
    max_tokens=2000       # 充足的输出长度
)
```

#### 3.2.2 异常处理
```python
try:
    response = client.chat.completions.create(...)
    return response.choices[0].message.content
except Exception as e:
    return f"分析失败：{str(e)}"
```

## 4. 系统实现

### 4.1 依赖管理
```python
# 核心依赖
import pandas as pd              # 数据处理
import json                      # 结果序列化
from volcenginesdkarkruntime import Ark  # 豆包模型客户端
from loguru import logger        # 日志管理
import dotenv                    # 环境变量管理
```

### 4.2 配置管理
```python
# 环境变量加载
dotenv.load_dotenv()

# 配置项
- ARK_API_KEY: 豆包模型API密钥
- ARK_API_BASE: 豆包模型API基础URL
- ARK_MODEL: 豆包模型名称
```

### 4.3 日志系统
```python
# 使用loguru进行结构化日志记录
logger.info("开始分析Su为0的失败记录...")
logger.info(f"找到 {len(failed_records)} 条Su为0的失败记录")
logger.info(f"正在分析第 {cnt+1} 条失败记录...")
```

## 5. 部署与运行

### 5.1 环境准备
```bash
# 1. 安装依赖
pip install pandas volcenginesdkarkruntime loguru python-dotenv

# 2. 配置环境变量
echo "ARK_API_KEY=your_api_key" >> .env
echo "ARK_API_BASE=https://ark.cn-beijing.volces.com/api/v3" >> .env  
echo "ARK_MODEL=doubao-seed-1-6-250615" >> .env
```

### 5.2 数据准备
确保以下数据文件存在：
- `/results/multiagent_uitars_results.csv` - 评估结果
- `/data/extracted_traces/mul-uitar-trace.csv` - 智能体轨迹
- `/data/groudtruth/gt.csv` - 人类基准数据

### 5.3 执行分析
```bash
python analyze_failures.py
```

### 5.4 输出结果
- **控制台输出**: 实时分析进度和结果摘要
- **JSON文件**: `failure_analysis_results.json` - 完整分析结果
- **日志文件**: 详细的执行日志

## 6. 性能与扩展

### 6.1 性能特征
- **处理能力**: 支持批量分析多个失败案例
- **响应时间**: 单个案例分析约10-30秒（取决于网络和模型响应）
- **准确性**: 基于豆包模型的专业分析，提供深度技术洞察

### 6.2 扩展性设计
- **模型可替换**: 支持切换不同的AI分析模型
- **数据源可扩展**: 支持添加新的数据源和评估指标
- **分析维度可定制**: 可根据需求调整分析框架

### 6.3 未来优化方向
1. **批量处理优化**: 实现并行分析提升效率
2. **缓存机制**: 避免重复分析相同案例
3. **可视化输出**: 生成图表和可视化报告
4. **实时监控**: 集成到CI/CD流程中进行持续监控

## 7. 使用示例

### 7.1 基本使用
```python
# 直接运行完整分析
python analyze_failures.py

# 输出示例
2025-06-27 17:01:57.783 | INFO | 开始分析Su为0的失败记录...
2025-06-27 17:01:57.799 | INFO | 找到 4 条Su为0的失败记录
2025-06-27 17:01:57.799 | INFO | 正在分析第 1 条失败记录...
2025-06-27 17:01:57.799 | INFO | 任务: 陶喆的包含Oh Baby Baby Baby歌词的歌...
```

### 7.2 结果解读
生成的JSON文件包含每个失败案例的详细分析：
```json
{
  "agent_id": 7,
  "human_id": 7, 
  "instruction": "陶喆的包含Oh Baby Baby Baby歌词的歌...",
  "analysis": "详细的AI分析结果..."
}
```

---

**文档版本**: v1.0  
**最后更新**: 2025年6月27日  
**维护团队**: AI评估技术团队
