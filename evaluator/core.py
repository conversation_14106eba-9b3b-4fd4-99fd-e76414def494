from typing import List, Optional
import openai
# from openai import OpenAI
from volcenginesdkarkruntime import Ark
import pandas as pd
from tqdm import tqdm
from dotenv import load_dotenv
from config.eval_config import EvalConfig

class EvaluatorCore:
    def __init__(self, config: EvalConfig):
        self.config = config
        load_dotenv()
        self._setup_llm()

    def _setup_llm(self):
        """配置LLM客户端"""
        if self.config.llm_provider == "ark":
            # 验证ARK配置
            if not self.config.ark_api_key or not self.config.ark_model:
                raise ValueError("使用ARK时请设置 ARK_API_KEY 和 ARK_MODEL 环境变量")

            # 创建ARK客户端
            self.ark_client = Ark(
                base_url=self.config.ark_api_base,
                api_key=self.config.ark_api_key,
            )
        else:
            # 验证传统OpenAI配置
            if not self.config.openai_api_key or not self.config.openai_model:
                raise ValueError("请设置 OPENAI_API_KEY 和 OPENAI_MODEL")

            if self.config.llm_provider == "azure":
                openai.api_key = self.config.openai_api_key
                openai.api_type = "azure"
                openai.api_base = self.config.openai_api_base
                openai.api_version = self.config.openai_api_version or "2023-05-15"
            elif self.config.llm_provider == "openrouter":
                openai.api_key = self.config.openrouter_api_key
                openai.api_base = self.config.openrouter_api_base
            else:  # 默认openai
                openai.api_key = self.config.openai_api_key
                if self.config.openai_api_base:
                    openai.api_base = self.config.openai_api_base

    def evaluate_with_llm(self, prompt: str) -> str:
        """评估单个prompt"""
        messages = [
            {"role": "system", "content": "你是一个负责评估mobile-use-agent的助手。"},
            {"role": "user", "content": prompt},
        ]

        if self.config.llm_provider == "ark":
            # 使用方舟ARK豆包模型
            response = self.ark_client.chat.completions.create(
                model=self.config.ark_model,
                messages=messages,
                temperature=0.0
            )
            return response.choices[0].message.content
        else:
            # 使用传统OpenAI API
            params = {
                "messages": messages,
                "temperature": 0.0
            }

            if self.config.llm_provider == "azure":
                params["engine"] = self.config.openai_model
            else:
                params["model"] = self.config.openai_model

            response = openai.ChatCompletion.create(**params)
            return response.choices[0].message['content']

    def build_prompt(self, instruction: str, agent_actions: List[str], human_actions: List[str],
                agent_ai_summaries: str, agent_final_action_type: str, human_result: str,
                 agent_steps: int, human_steps: int) -> str:
        return f"""
用户核心指令：{instruction}（请重点关注指令中的核心目标、关键约束条件与质量要求）

人类执行记录（共 {human_steps} 步）：
执行步骤：{chr(10).join(f"Step {i+1}: {step}" for i, step in enumerate(human_actions))}
最终输出结果：{human_result}（注：包含结果内容、格式、完整性等全部特征）

智能体执行记录（共 {agent_steps} 步）：
执行步骤：{chr(10).join(f"Step {i+1}: {step}" for i, step in enumerate(agent_actions))}
智能体总结内容：{agent_ai_summaries}（智能体对任务执行的摘要描述）
最终动作类型：{agent_final_action_type}（如"提交结果"/"终止任务"/"需要补充信息"等）

请基于以下专业维度对智能体执行质量进行量化评估（评分需严格依据客观记录，避免主观臆断）：

1. 任务完成度（Su, Success）[0/1分]
   判断依据：智能体摘要输出是否**满足用户指令的核心目标**。需同时满足以下两点：
   - 摘要结果匹配：{agent_ai_summaries} 与 {human_result} 在**语义上完全一致**，即使描述方式不同，只要**关键信息、格式、完整性**相符，即视为匹配；
     提醒：不要因词句不一致而误判，判断依据应为“是否达到相同目标”而非语言表述完全一致。
   - 动作有效性：{agent_final_action_type} 属于完成该任务的合理终止类型。

   满足以上两项则 Su=1，否则 Su=0（部分满足即视为未完成）

2. 过程正确性（PS, Process Score）[0-1分，保留2位小数]
   判断定义：智能体每一步操作对实现任务目标的有效性（排除冗余/错误/无效操作）
   有效步骤判定标准（需同时满足）：
   - 目标相关性：该步骤直接服务于用户指令的核心目标（如用户要"查询订单"，则"打开订单页面"是有效步骤，"查看通知栏"是无效步骤）；
   - 操作正确性：步骤描述无逻辑错误（如"输入密码123"但实际应为"输入验证码"）、无违反系统规则（如"删除未保存文件"属于违规操作）；
   - 流程必要性：该步骤是完成任务的必要环节（重复执行同一操作视为冗余步骤）；
   计算方式：PS = 智能体有效步骤数 / 智能体总步骤数（agent_steps）

3. 相对效率（RE, Relative Efficiency）[0+，保留2位小数]
   计算逻辑：衡量智能体与人类完成相同任务的步骤效率差异
   公式定义：RE = 智能体总步骤数（agent_steps） / 人类总步骤数（human_steps）
   特殊说明：
   - 若RE < 1：表示智能体比人类更高效（需结合PS判断是否因跳过必要步骤导致）；
   - 若RE = 1：表示步骤效率与人类一致；
   - 若RE > 1：表示智能体步骤冗余（需检查是否存在重复操作或无效探索）

4. 策略一致性（CR, Completion Rate）[0-1分，保留2位小数]
   判断定义：智能体操作步骤与人类最优策略的重合程度（反映智能体是否模仿人类合理策略）
   重合步骤判定标准（满足其一即可）：
   - 操作意图一致：步骤目标相同（如"搜索商品"与"查询商品信息"视为意图一致）；
   - 操作对象一致：作用于同一系统组件（如"点击支付按钮"与"触发支付流程"视为对象一致）；
   - 操作结果一致：执行后系统状态变化相同（如"输入地址A"与"填写收货地址A"视为结果一致）；
   计算方式：CR = （智能体与人类重合的步骤数） / 人类总步骤数（human_steps）

请严格按照以下格式输出（数值均保留2位小数，无四舍五入）：
Su: 0 或 1  
PS: 有效步骤数 / 总步骤数 = ?
RE: agent_steps / human_steps = {agent_steps} / {human_steps} = {agent_steps / human_steps:.2f}
CR: 重合步骤数 / human_steps  = ?
evaluate_reason: [请结合上面四项维度给出客观、明确、合理的评估理由]
""".strip()
        
    def batch_evaluate(self, agent_csv: str, human_csv: str, save_path: str = "res.csv") -> pd.DataFrame:
        agent_df = pd.read_csv(agent_csv)
        human_df = pd.read_csv(human_csv)

        results = []
        total_evaluations = len(agent_df)
        pbar = tqdm(total=total_evaluations, desc="Evaluating")
        for _, human_row in human_df.iterrows():
            instruction = human_row['instruction']
            human_id = human_row['id']  # 获取人类记录的ID
            task_type = human_row['步骤分级']
            app_category = human_row['应用分类']
            human_actions = eval(human_row['action_trace'])
            human_result = human_row['expected_result']
            human_steps = int(human_row['human_steps'])

            agent_rows = agent_df[agent_df['instruction'] == instruction]
            if agent_rows.empty:
                continue
                
          
            # 遍历所有匹配的智能体记录
            for _, agent_row in agent_rows.iterrows():
                agent_id = agent_row['id']  # 获取智能体记录的ID
                agent_actions = eval(agent_row['actions'])
                agent_final_action_type = agent_row['final_action_type']
                # agent_final_action_content = agent_row['final_action_content']
                agent_ai_summaries = agent_row['ai_summaries']
                agent_steps = int(agent_row['num_steps'])
                
                prompt = self.build_prompt(
                    instruction,
                    agent_actions,
                    human_actions,
                    agent_final_action_type,
                    # agent_final_action_content,
                    agent_ai_summaries,
                    human_result,
                    agent_steps,
                    human_steps
                )
                llm_output = self.evaluate_with_llm(prompt)

                result = {
                    "instruction": instruction,
                    "human_id": human_id,
                    "agent_id": agent_id,  
                    "task_type": task_type,
                    "app_category": app_category,
                    "human_steps": human_steps,
                    "agent_steps": agent_steps,

                    # "llm_output": llm_output,
                }

                for line in llm_output.strip().split("\n"):
                    if ":" in line:
                        k, v = line.split(":", 1)
                        k = k.strip()
                        v = v.strip()
                        
                        # 特殊处理PS指标，只提取数值部分
                        if k == "PS" and "=" in v:
                            try:
                                # 提取最后一个等号后的数值
                                result[k] = float(v.split("=")[-1].strip())
                            except ValueError:
                                result[k] = 0.0  # 默认值
                        else:
                            try:
                                result[k] = float(v)
                            except ValueError:
                                result[k] = v

                results.append(result)
                pbar.update(1)

        pbar.close()
        df = pd.DataFrame(results)
        df.to_csv(save_path, index=False)
        return df