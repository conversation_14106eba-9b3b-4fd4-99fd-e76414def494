#!/usr/bin/env python3
"""
Mobile Agent Evaluation System - 移动智能体评估系统
支持完整的数据提取、处理和评估流程，包含详细的日志记录和错误处理
"""
import argparse
import logging
import os
import pandas as pd
import sys
from pathlib import Path
from datetime import datetime
from typing import Optional

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from config.eval_config import EvalConfig
from evaluator.core import EvaluatorCore
from extractor.trace_parser import extract_all_from_csv


def setup_logger():
    """设置日志配置"""
    from logging.handlers import RotatingFileHandler

    # 创建logs目录
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)

    # 配置日志格式
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

    # 创建logger
    logger = logging.getLogger('mobile_agent_eval')
    logger.setLevel(logging.DEBUG)

    # 清除已有的处理器
    logger.handlers.clear()

    # 控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)

    # 文件处理器
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = log_dir / f"eval_{timestamp}.log"
    file_handler = RotatingFileHandler(
        log_file, maxBytes=10*1024*1024, backupCount=5, encoding='utf-8'
    )
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)

    return logger


# def validate_environment() -> bool:
#     """验证运行环境和必要配置"""
#     logger = logging.getLogger('mobile_agent_eval')

#     # 加载.env文件
#     from dotenv import load_dotenv
#     load_dotenv()

#     # 获取LLM提供商
#     llm_provider = os.getenv("LLM_PROVIDER", "openai")
#     logger.info(f"🔍 检测到LLM提供商: {llm_provider}")

#     # 根据提供商检查相应的环境变量
#     missing_vars = []

#     if llm_provider == "ark":
#         # ARK豆包模型配置
#         required_vars = ["ARK_API_KEY"]
#         for var in required_vars:
#             if not os.getenv(var):
#                 missing_vars.append(var)

#         # 显示ARK配置信息
#         ark_model = os.getenv("ARK_MODEL", "doubao-seed-1-6-250615")
#         ark_base = os.getenv("ARK_API_BASE", "https://ark.cn-beijing.volces.com/api/v3")
#         logger.info(f"  ARK模型: {ark_model}")
#         logger.info(f"  ARK端点: {ark_base}")

#     elif llm_provider == "azure":
#         # Azure OpenAI配置
#         required_vars = ["OPENAI_API_KEY", "OPENAI_API_BASE", "OPENAI_API_VERSION"]
#         for var in required_vars:
#             if not os.getenv(var):
#                 missing_vars.append(var)

#     elif llm_provider == "openrouter":
#         # OpenRouter配置
#         required_vars = ["OPENROUTER_API_KEY"]
#         for var in required_vars:
#             if not os.getenv(var):
#                 missing_vars.append(var)

#     else:
#         # 默认OpenAI配置
#         required_vars = ["OPENAI_API_KEY"]
#         for var in required_vars:
#             if not os.getenv(var):
#                 missing_vars.append(var)

#     if missing_vars:
#         logger.error(f"❌ 缺少必要的环境变量: {', '.join(missing_vars)}")
#         logger.error("请设置以下环境变量:")
#         for var in missing_vars:
#             logger.error(f"  export {var}='your-value-here'")

#         # 提供配置示例
#         if llm_provider == "ark":
#             logger.error("\n💡 ARK配置示例:")
#             logger.error("  export LLM_PROVIDER=ark")
#             logger.error("  export ARK_API_KEY='your-ark-api-key'")
#             logger.error("  export ARK_MODEL='doubao-seed-1-6-250615'")

#         return False

#     logger.info("✅ 环境变量验证通过")
#     return True


# def validate_input_files(input_path: Path, human_csv_path: str) -> bool:
#     """验证输入文件是否存在"""
#     logger = logging.getLogger('mobile_agent_eval')

#     if not input_path.exists():
#         logger.error(f"❌ 输入文件不存在: {input_path}")
#         return False

#     if not Path(human_csv_path).exists():
#         logger.error(f"❌ 人类基准数据文件不存在: {human_csv_path}")
#         return False

#     logger.info(f"✅ 输入文件验证通过: {input_path}")
#     logger.info(f"✅ 基准数据文件验证通过: {human_csv_path}")
#     return True



# =========================================================
# 1) 新增：校验已提取 CSV 的工具函数
# =========================================================
REQUIRED_AGENT_COLS = {
    "id",
    "instruction",
    "actions",
    "final_action_type",
    "ai_summaries",
    "num_steps",
}

def validate_extracted_csv(csv_path: Path) -> bool:
    """检查已提取智能体 CSV 是否存在且列完备"""
    logger = logging.getLogger("mobile_agent_eval")

    if not csv_path.exists():
        logger.error(f"❌ 指定的智能体 CSV 不存在: {csv_path}")
        return False

    try:
        df = pd.read_csv(csv_path, nrows=5)  # 只读几行即可
    except Exception as e:
        logger.error(f"❌ 无法读取 {csv_path}: {e}")
        return False

    missing = REQUIRED_AGENT_COLS - set(df.columns)
    if missing:
        logger.error(f"❌ 智能体 CSV 缺少必要列: {', '.join(sorted(missing))}")
        return False

    logger.info(f"✅ 已提取 CSV 校验通过: {csv_path}")
    return True



def extract_agent_data(input_path: Path, output_dir: Path) -> Optional[str]:
    """提取智能体数据"""
    logger = logging.getLogger('mobile_agent_eval')

    try:
        logger.info("🔄 开始提取智能体数据...")

        # 创建提取数据的输出目录
        extracted_dir = output_dir / "extracted_traces"
        extracted_dir.mkdir(parents=True, exist_ok=True)

        # 生成输出文件路径
        timestamp = datetime.now().strftime("%m%d_%H%M")
        output_base = extracted_dir / f"agent_trace_{timestamp}"

        logger.info(f"📂 输入文件: {input_path}")
        logger.info(f"📤 输出路径: {output_base}")

        # 执行数据提取
        df_metrics, saved_files = extract_all_from_csv(
            csv_path=str(input_path),
            save_path=str(output_base),
            output_format='csv'
        )

        if not saved_files:
            logger.error("❌ 数据提取失败，没有生成输出文件")
            return None

        agent_csv_path = saved_files[0]  # 获取CSV文件路径

        logger.info(f"✅ 数据提取完成，共 {len(df_metrics)} 条记录")
        logger.info(f"📄 智能体数据文件: {agent_csv_path}")

        # # 显示数据统计
        # if '应用分类' in df_metrics.columns:
        #     app_counts = df_metrics['应用分类'].value_counts()
        #     logger.info(f"📊 应用分类分布: {dict(app_counts.head(3))}")

        # if 'is_success' in df_metrics.columns:
        #     success_rate = df_metrics['is_success'].mean() * 100
        #     logger.info(f"📊 任务成功率: {success_rate:.1f}%")

        return agent_csv_path

    except Exception as e:
        logger.error(f"❌ 数据提取失败: {e}")
        logger.exception("详细错误信息:")
        return None


def run_evaluation(config: EvalConfig, agent_csv: str, human_csv: str,
                  output_path: Path) -> Optional[object]:
    """运行评估流程"""
    logger = logging.getLogger('mobile_agent_eval')

    try:
        logger.info("🚀 开始评估流程...")

        # 创建评估器
        evaluator = EvaluatorCore(config)
        logger.info("✅ 评估器初始化完成")

        # 执行批量评估
        logger.info(f"📊 智能体数据: {agent_csv}")
        logger.info(f"📊 人类基准数据: {human_csv}")
        logger.info(f"📤 结果输出路径: {output_path}")

        results = evaluator.batch_evaluate(
            agent_csv=agent_csv,
            human_csv=human_csv,
            save_path=str(output_path)
        )

        logger.info(f"✅ 评估完成，共处理 {len(results)} 条记录")
        logger.info(f"📄 结果已保存至: {output_path}")

        # 显示评估结果统计
        if 'Su' in results.columns:
            success_rate = results['Su'].mean() * 100
            logger.info(f"📊 任务成功率 (Su): {success_rate:.1f}%")

        if 'PS' in results.columns:
            avg_ps = results['PS'].mean()
            logger.info(f"📊 平均步骤有效性 (PS): {avg_ps:.2f}")

        return results

    except Exception as e:
        logger.error(f"❌ 评估失败: {e}")
        logger.exception("详细错误信息:")
        return None


def main():
    """主函数"""
    # 设置日志
    logger = setup_logger()
    logger.info("=" * 60)
    logger.info("🚀 Mobile Agent Evaluation System 启动")
    logger.info("=" * 60)

    try:
        # 解析命令行参数
        parser = argparse.ArgumentParser(description="Mobile Agent Evaluation System")
        parser.add_argument("--input", type=Path,
                           default="data/raw_traces/multiagent.csv",
                           help="Path to raw trace CSV file")
        parser.add_argument("--eval-output", type=Path, default="results",
                           help="Output directory for evaluation results")
        parser.add_argument("--extract-output", type=Path, default="data/extracted_traces",
                           help="Output directory for extracted traces")
        parser.add_argument("--human-csv", type=str,
                           default="data/groudtruth/gt.csv",
                           help="Path to human ground truth CSV file")
        
        # 跳过参数配置
        parser.add_argument("--skip-extract", action="store_true",
                        help="Skip data-extraction phase and use pre-extracted CSV")
        parser.add_argument("--agent-csv", type=Path, default="data/extracted_traces/mul-uitar-trace.csv",
                        help="Path to pre-extracted agent CSV (required with --skip-extract)")
        args = parser.parse_args()

        logger.info(f"📋 命令行参数:")
        logger.info(f"  输入文件: {args.input}")
        logger.info(f"  输出目录: {args.extract_output}")
        logger.info(f"  人类基准数据: {args.human_csv}")

        # 确保输出目录存在
        args.extract_output.mkdir(parents=True, exist_ok=True)
        logger.info(f"✅ 输出目录已创建: {args.extract_output}")

        # 第一阶段：提取智能体数据
        if args.skip_extract:
                logger.info("=" * 50)
                logger.info("📊 第一阶段：数据提取")
                logger.info("⏩ 已有提取数据，跳过数据提取阶段 (--skip-extract)")
                if args.agent_csv is None:
                    logger.error("❌ 必须通过 --agent-csv 指定已提取的智能体 CSV 文件")
                    sys.exit(1)
                if not validate_extracted_csv(args.agent_csv):
                    logger.error("❌ 已提取 CSV 校验失败，程序退出")
                    sys.exit(1)
                agent_csv_path = str(args.agent_csv)   # ↓ 后续保持字符串
        else:
            logger.info("=" * 50)
            logger.info("📊 第一阶段：数据提取")
            agent_csv_path = extract_agent_data(args.input, args.extract_output)
            if not agent_csv_path:
                logger.error("❌ 数据提取阶段失败，程序退出")
                sys.exit(1)
        logger.info(f"📄 使用的智能体 CSV: {agent_csv_path}")

        # 第二阶段：配置评估系统
        logger.info("="*50)
        logger.info("⚙️  第二阶段：配置评估系统")
      

        # 创建配置对象（统一从环境变量读取）
        config = EvalConfig(
            raw_traces_dir=args.input,
            extracted_traces_dir=args.extract_output
        )

        logger.info(f"✅ 评估配置完成:")
        logger.info(f"  LLM提供商: {config.llm_provider}")

        # 根据提供商显示相应的模型信息
        if config.llm_provider == "ark":
            logger.info(f"  ARK模型: {config.ark_model}")
            logger.info(f"  ARK端点: {config.ark_api_base}")
        elif config.llm_provider == "openrouter":
            logger.info(f"  OpenRouter模型: {config.openrouter_model}")
        else:
            logger.info(f"  OpenAI模型: {config.openai_model}")

        # 第三阶段：执行评估
        logger.info("="*50)
        logger.info("🎯 第三阶段：执行评估")

        results_path = args.eval_output / "multiagent_doubao_results.csv"
        results = run_evaluation(config, agent_csv_path, args.human_csv, results_path)

        if results is None:
            logger.error("❌ 评估阶段失败，程序退出")
            sys.exit(1)

        logger.info("🎉 评估系统运行完成！")
        logger.info("="*60)
        logger.info(f"📊 处理记录数: {len(results)}")
        logger.info(f"📄 结果文件: {results_path}")
        logger.info(f"📁 输出目录: {args.eval_output}")

    except KeyboardInterrupt:
        logger.warning("⚠️  用户中断程序执行")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ 程序执行失败: {e}")
        logger.exception("详细错误信息:")
        sys.exit(1)


if __name__ == "__main__":
    main()
