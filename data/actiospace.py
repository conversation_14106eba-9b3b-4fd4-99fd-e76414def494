from enum import Enum

class ActionSpace(Enum):
    LONG_POINT = 0
    NO_ACTION = 1
    UNUSED_2 = 2
    TYPE = 3
    DUAL_POINT = 4
    PRESS_BACK = 5
    PRESS_HOME = 6
    PRESS_ENTER = 7
    UNUSED_8 = 8
    UNUSED_9 = 9
    STATUS_TASK_COMPLETE = 10
    STATUS_TASK_IMPOSSIBLE = 11

    @staticmethod
    def get_description(action):
        descriptions = {
            ActionSpace.LONG_POINT: "长按触摸（long point）",
            ActionSpace.NO_ACTION: "无操作",
            ActionSpace.UNUSED_2: "保留（未使用）",
            ActionSpace.TYPE: "文本输入（只发送文本，不含聚焦或提交操作）",
            ActionSpace.DUAL_POINT: "双点手势（如拖拽、缩放等复合手势）",
            ActionSpace.PRESS_BACK: "按下“返回”键（ADB 模拟 Back 按键）",
            ActionSpace.PRESS_HOME: "按下“主屏”键（ADB 模拟 Home 按键）",
            ActionSpace.PRESS_ENTER: "按下“回车”键（ADB 模拟 Enter 按键）",
            ActionSpace.UNUSED_8: "保留（未使用）",
            ActionSpace.UNUSED_9: "保留（未使用）",
            ActionSpace.STATUS_TASK_COMPLETE: "任务完成状态（任务已完成或无需再做）",
            ActionSpace.STATUS_TASK_IMPOSSIBLE: "任务不可完成状态（因界面差异等原因无法继续）",
        }
        return descriptions.get(action, "未知动作")

# 示例用法
action = ActionSpace.LONG_POINT
print(f"动作: {action.name}, 含义: {ActionSpace.get_description(action)}")