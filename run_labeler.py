#!/usr/bin/env python3
"""
LLM标签系统运行脚本
使用方法: python run_labeler.py
"""
import sys
import os

# 添加extractor目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'extractor'))

from batch_labeler import main

if __name__ == "__main__":
    print("🚀 启动LLM标签系统...")
    print("请确保已设置OPENAI_API_KEY环境变量")
    print("=" * 50)
    
    # 检查API密钥
    if not os.getenv('OPENAI_API_KEY'):
        print("❌ 错误: 未设置OPENAI_API_KEY环境变量")
        print("请运行: export OPENAI_API_KEY='your-api-key-here'")
        sys.exit(1)
    
    try:
        main()
    except KeyboardInterrupt:
        print("\n⚠️  用户中断程序")
    except Exception as e:
        print(f"\n❌ 程序执行失败: {e}")
        sys.exit(1)
