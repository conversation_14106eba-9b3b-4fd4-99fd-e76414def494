.DS_Store
**/.DS_Store  
.ruff_cache/
.venv/
.DS_Store
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
.env
.venv
env/
venv/
ENV/
.uv/
.pytest_cache/
.coverage
htmlcov/
.idea/
.vscode/
logs/
*.csv
*.html
*.json
*.jsonl
*.log
*.png


mcp.json
debug-mcp.sh
/bin
/mcp-server/
mobile-agent-zip-*.zip
0618.csv
data/extracted_traces/agent_trace.csv
data/extracted_traces/enhanced_agent_trace.csv
data/groudtruth/groudtruth.csv
data/groudtruth/human_trace_labeled.csv
data/groudtruth/human_trace.csv
data/groudtruth/human_trace.jsonl
data/extracted_traces/enhanced_agent_trace.jsonl
test_results/extracted_traces/agent_trace_0618_2157.csv
test_results_2/extracted_traces/agent_trace_0618_2157.csv
