#!/usr/bin/env python3
"""
生成综合性的可视化报告，包含雷达图、分布图、热力图和趋势图
"""
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import re
from pathlib import Path
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class EvaluationDashboard:
    """评估结果可视化仪表板"""
    
    def __init__(self, csv_path: str):
        """初始化仪表板"""
        self.csv_path = csv_path
        self.df = None
        self.output_dir = Path("visualization/multiagent_uitars_output")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
    def load_and_preprocess_data(self):
        """加载并预处理数据"""
        print("📊 加载数据...")
        self.df = pd.read_csv(self.csv_path)
        
        # 解析分数格式的字段
        self._parse_score_fields()

        # 处理应用分类（直接使用CSV中的app_category字段）
        self._process_app_categories()

        # 计算统计指标
        self._calculate_metrics()
        
        print(f"✅ 数据加载完成，共 {len(self.df)} 条记录")
        
    def _parse_score_fields(self):
        """解析PS、RE、CR字段的分数格式"""
        for col in ['PS', 'RE', 'CR']:
            if col in self.df.columns:
                # 提取等号后的数值
                self.df[f'{col}_numeric'] = self.df[col].apply(self._extract_numeric_value)
        
    def _extract_numeric_value(self, value):
        """从分数字符串中提取数值"""
        if pd.isna(value):
            return np.nan
        
        # 如果已经是数值，直接返回
        if isinstance(value, (int, float)):
            return float(value)
        
        # 提取等号后的数值
        str_value = str(value)
        if '=' in str_value:
            try:
                return float(str_value.split('=')[-1].strip())
            except:
                pass
        
        # 尝试直接转换
        try:
            return float(str_value)
        except:
            return np.nan
    
    # TODO: 支持自定义应用分类 + label
    def _process_app_categories(self):
        """处理应用分类（直接使用CSV中的app_category字段）"""
        # 检查是否存在app_category字段
        if 'app_category' not in self.df.columns:
            print("⚠️  警告：CSV文件中未找到app_category字段，将使用默认分类")
            self.df['app_category'] = '未分类'
            return

        # 清理和标准化应用分类名称
        self.df['app_category'] = self.df['app_category'].fillna('未分类')

        # 统计分类信息
        category_counts = self.df['app_category'].value_counts()
        print(f"📊 应用分类统计:")
        for category, count in category_counts.items():
            print(f"  {category}: {count} 个任务")

        print(f"✅ 使用CSV中的应用分类字段，共 {len(category_counts)} 个分类")
        
    def _calculate_metrics(self):
        """计算统计指标"""
        # 成功率
        self.success_rate = self.df['Su'].mean() * 100
        
        # 各指标平均值
        self.avg_metrics = {
            'Su': self.df['Su'].mean(),
            'PS': self.df['PS_numeric'].mean(),
            'RE': self.df['RE_numeric'].mean(), 
            'CR': self.df['CR_numeric'].mean()
        }
        
        # 应用分类统计
        self.app_stats = self.df.groupby('app_category').agg({
            'Su': 'mean',
            'PS_numeric': 'mean',
            'RE_numeric': 'mean',
            'CR_numeric': 'mean'
        }).round(3)
        
    def create_radar_chart(self):
        """创建雷达图展示四维度综合表现"""
        print("🎯 生成雷达图...")
        
        # 准备数据
        metrics = ['任务完成度(Su)', '过程正确性(PS)', '相对效率(RE)', '策略一致性(CR)']
        values = [
            self.avg_metrics['Su'],
            self.avg_metrics['PS'], 
            1/self.avg_metrics['RE'] if self.avg_metrics['RE'] > 0 else 0,  # RE越小越好，取倒数
            self.avg_metrics['CR']
        ]
        
        # 创建雷达图
        fig = go.Figure()
        
        fig.add_trace(go.Scatterpolar(
            r=values + [values[0]],  # 闭合图形
            theta=metrics + [metrics[0]],
            fill='toself',
            name='智能体表现',
            line_color='rgb(0, 123, 255)',
            fillcolor='rgba(0, 123, 255, 0.3)'
        ))
        
        fig.update_layout(
            polar=dict(
                radialaxis=dict(
                    visible=True,
                    range=[0, 1]
                )
            ),
            title={
                'text': '移动智能体四维度综合表现雷达图',
                'x': 0.5,
                'font': {'size': 20}
            },
            font=dict(size=14),
            width=800,
            height=600
        )
        
        # 保存图表
        output_path = self.output_dir / "radar_chart.html"
        fig.write_html(str(output_path))
        
        # 保存静态图片
        img_path = self.output_dir / "radar_chart.png"
        fig.write_image(str(img_path), width=800, height=600, scale=2)
        
        print(f"✅ 雷达图已保存: {output_path}")
        return fig
        
    def create_distribution_plots(self):
        """创建各指标分布图"""
        print("📈 生成分布图...")
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('智能体评估指标分布情况', fontsize=16, fontweight='bold')
        
        # Su分布 - 使用离散柱状图，柔和颜色
        su_counts = self.df['Su'].value_counts().sort_index()
        su_labels = ['失败 (0)', '成功 (1)']
        su_values = [su_counts.get(0, 0), su_counts.get(1, 0)]
        su_colors = ['#FF9999', '#99CC99']  # 柔和的红色和绿色

        bars = axes[0,0].bar(su_labels, su_values, alpha=0.8, color=su_colors, edgecolor='white', linewidth=1.5)
        axes[0,0].set_title('任务完成度(Su)分布')
        axes[0,0].set_xlabel('完成度')
        axes[0,0].set_ylabel('频次')

        # 在柱子上显示数值
        for bar, value in zip(bars, su_values):
            axes[0,0].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5,
                          str(value), ha='center', va='bottom', fontweight='bold')
        
        # PS分布
        axes[0,1].hist(self.df['PS_numeric'].dropna(), bins=20, alpha=0.7, color='lightgreen', edgecolor='black')
        axes[0,1].set_title('过程正确性(PS)分布')
        axes[0,1].set_xlabel('正确性得分')
        axes[0,1].set_ylabel('频次')
        
        # RE分布
        axes[1,0].hist(self.df['RE_numeric'].dropna(), bins=20, alpha=0.7, color='orange', edgecolor='black')
        axes[1,0].set_title('相对效率(RE)分布')
        axes[1,0].set_xlabel('效率比值')
        axes[1,0].set_ylabel('频次')
        
        # CR分布
        axes[1,1].hist(self.df['CR_numeric'].dropna(), bins=20, alpha=0.7, color='pink', edgecolor='black')
        axes[1,1].set_title('策略一致性(CR)分布')
        axes[1,1].set_xlabel('一致性得分')
        axes[1,1].set_ylabel('频次')
        
        plt.tight_layout()
        
        # 保存图表
        output_path = self.output_dir / "distribution_plots.png"
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"✅ 分布图已保存: {output_path}")
        
    def create_heatmap(self):
        """创建应用类型vs性能指标热力图"""
        print("🔥 生成热力图...")
        
        # 准备热力图数据
        heatmap_data = self.app_stats.copy()
        
        # 创建热力图
        plt.figure(figsize=(12, 8))
        sns.heatmap(
            heatmap_data.T,  # 转置，指标作为行
            annot=True,
            fmt='.3f',
            cmap='RdYlBu_r',
            center=0.5,
            square=True,
            linewidths=0.5,
            cbar_kws={"shrink": .8}
        )
        
        plt.title('不同应用类型的性能表现热力图', fontsize=16, fontweight='bold', pad=20)
        plt.xlabel('应用类型', fontsize=12)
        plt.ylabel('评估指标', fontsize=12)
        plt.xticks(rotation=45)
        plt.yticks(rotation=0)
        
        plt.tight_layout()
        
        # 保存图表
        output_path = self.output_dir / "performance_heatmap.png"
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"✅ 热力图已保存: {output_path}")
        
    def create_trend_chart(self):
        """创建任务序列性能趋势图"""
        print("📊 生成趋势图...")
        
        # 按human_id排序
        df_sorted = self.df.sort_values('human_id').reset_index(drop=True)
        
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=('任务完成度趋势', '过程正确性趋势', '相对效率趋势', '策略一致性趋势'),
            vertical_spacing=0.1
        )
        
        # Su趋势
        fig.add_trace(
            go.Scatter(x=df_sorted.index, y=df_sorted['Su'], 
                      mode='lines+markers', name='Su', line=dict(color='blue')),
            row=1, col=1
        )
        
        # PS趋势
        fig.add_trace(
            go.Scatter(x=df_sorted.index, y=df_sorted['PS_numeric'], 
                      mode='lines+markers', name='PS', line=dict(color='green')),
            row=1, col=2
        )
        
        # RE趋势
        fig.add_trace(
            go.Scatter(x=df_sorted.index, y=df_sorted['RE_numeric'], 
                      mode='lines+markers', name='RE', line=dict(color='orange')),
            row=2, col=1
        )
        
        # CR趋势
        fig.add_trace(
            go.Scatter(x=df_sorted.index, y=df_sorted['CR_numeric'], 
                      mode='lines+markers', name='CR', line=dict(color='red')),
            row=2, col=2
        )
        
        fig.update_layout(
            title_text="智能体性能指标趋势分析",
            title_x=0.5,
            height=800,
            showlegend=False
        )
        
        # 保存图表
        output_path = self.output_dir / "trend_analysis.html"
        fig.write_html(str(output_path))
        
        img_path = self.output_dir / "trend_analysis.png"
        fig.write_image(str(img_path), width=1200, height=800, scale=2)
        
        print(f"✅ 趋势图已保存: {output_path}")
        return fig

    def create_summary_report(self):
        """生成数据洞察总结报告"""
        print("📋 生成总结报告...")

        # 计算关键统计数据
        total_tasks = len(self.df)
        success_count = self.df['Su'].sum()

        # 找出表现最好和最差的应用类型
        best_app = self.app_stats['Su'].idxmax()
        worst_app = self.app_stats['Su'].idxmin()

        # 效率分析
        efficient_tasks = (self.df['RE_numeric'] < 1.2).sum()
        inefficient_tasks = (self.df['RE_numeric'] > 1.5).sum()

        # 生成报告内容
        report = f"""
# 移动智能体评估结果分析报告

## 📊 总体表现概览

- **总任务数**: {total_tasks}
- **成功完成**: {success_count} 个任务
- **整体成功率**: {self.success_rate:.1f}%
- **平均过程正确性**: {self.avg_metrics['PS']:.3f}
- **平均相对效率**: {self.avg_metrics['RE']:.3f}
- **平均策略一致性**: {self.avg_metrics['CR']:.3f}

## 🎯 关键发现

### 任务完成情况
- 成功率达到 {self.success_rate:.1f}%，表明智能体在大多数任务上能够达成目标
- 共有 {success_count} 个任务完全成功，{total_tasks - success_count} 个任务未能完成

### 应用类型表现
- **表现最佳**: {best_app} (成功率: {self.app_stats.loc[best_app, 'Su']:.1%})
- **表现最差**: {worst_app} (成功率: {self.app_stats.loc[worst_app, 'Su']:.1%})

### 效率分析
- **高效任务** (RE < 1.2): {efficient_tasks} 个
- **低效任务** (RE > 1.5): {inefficient_tasks} 个
- 智能体在 {(efficient_tasks/total_tasks)*100:.1f}% 的任务中表现高效

## 📈 改进建议

1. **提升成功率**: 针对失败任务进行深入分析，优化算法逻辑
2. **优化效率**: 减少冗余步骤，提高操作效率
3. **增强一致性**: 学习人类最优策略，提高策略一致性
4. **应用适配**: 针对表现较差的应用类型进行专项优化

## 📊 详细数据

### 各应用类型详细表现
{self.app_stats.to_string()}

---
报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""

        # 保存报告
        report_path = self.output_dir / "analysis_report.md"
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report)

        print(f"✅ 分析报告已保存: {report_path}")
        return report

    def create_interactive_dashboard(self):
        """创建交互式HTML仪表板"""
        print("🌐 生成交互式仪表板...")

        # 创建子图布局
        fig = make_subplots(
            rows=3, cols=2,
            subplot_titles=(
                '任务完成度分布 (离散值)', '过程正确性分布',
                '相对效率分布', '策略一致性分布',
                '应用类型成功率对比', '指标相关性分析'
            ),
            specs=[
                [{"type": "histogram"}, {"type": "histogram"}],
                [{"type": "histogram"}, {"type": "histogram"}],
                [{"type": "bar"}, {"type": "scatter"}]
            ],
            vertical_spacing=0.08
        )

        # 添加分布图 - Su使用离散值显示，柔和颜色
        su_counts = self.df['Su'].value_counts().sort_index()
        fig.add_trace(
            go.Bar(
                x=['失败 (0)', '成功 (1)'],
                y=[su_counts.get(0, 0), su_counts.get(1, 0)],
                name='Su',
                marker_color=['#FF9999', '#99CC99'],  # 柔和的红色和绿色
                text=[su_counts.get(0, 0), su_counts.get(1, 0)],
                textposition='outside'
            ),
            row=1, col=1
        )

        fig.add_trace(
            go.Histogram(x=self.df['PS_numeric'].dropna(), name='PS', nbinsx=20),
            row=1, col=2
        )

        fig.add_trace(
            go.Histogram(x=self.df['RE_numeric'].dropna(), name='RE', nbinsx=20),
            row=2, col=1
        )

        fig.add_trace(
            go.Histogram(x=self.df['CR_numeric'].dropna(), name='CR', nbinsx=20),
            row=2, col=2
        )

        # 应用类型对比
        app_success = self.df.groupby('app_category')['Su'].mean()
        fig.add_trace(
            go.Bar(x=app_success.index, y=app_success.values, name='成功率'),
            row=3, col=1
        )

        # 指标相关性
        fig.add_trace(
            go.Scatter(
                x=self.df['PS_numeric'],
                y=self.df['CR_numeric'],
                mode='markers',
                name='PS vs CR',
                text=self.df['instruction'].str[:30] + '...',
                hovertemplate='PS: %{x}<br>CR: %{y}<br>任务: %{text}<extra></extra>'
            ),
            row=3, col=2
        )

        fig.update_layout(
            title_text="移动智能体评估结果交互式仪表板",
            title_x=0.5,
            height=1200,
            showlegend=False
        )

        # 保存交互式仪表板
        dashboard_path = self.output_dir / "interactive_dashboard.html"
        fig.write_html(str(dashboard_path))

        print(f"✅ 交互式仪表板已保存: {dashboard_path}")
        return fig

    def generate_all_visualizations(self):
        """生成所有可视化图表"""
        print("🚀 开始生成完整可视化报告...")
        print("=" * 60)

        # 加载数据
        self.load_and_preprocess_data()

        # 生成各类图表
        self.create_radar_chart()
        self.create_distribution_plots()
        self.create_heatmap()
        self.create_trend_chart()
        self.create_summary_report()
        self.create_interactive_dashboard()

        print("\n🎉 所有可视化图表生成完成！")
        print(f"📁 输出目录: {self.output_dir}")
        print("\n📊 生成的文件:")
        for file_path in self.output_dir.glob("*"):
            print(f"  📄 {file_path.name}")

        return self.output_dir


def main():
    """主函数"""
    import sys

    if len(sys.argv) != 2:
        print("使用方法: python dashboard.py <csv_file_path>")
        print("示例: python dashboard.py ../test_results_2/evaluation_results.csv")
        sys.exit(1)

    csv_path = sys.argv[1]

    if not Path(csv_path).exists():
        print(f"❌ 文件不存在: {csv_path}")
        sys.exit(1)

    # 创建仪表板
    dashboard = EvaluationDashboard(csv_path)

    try:
        # 生成所有可视化
        output_dir = dashboard.generate_all_visualizations()

        print(f"\n✅ 可视化报告生成成功！")
        print(f"🌐 打开 {output_dir}/interactive_dashboard.html 查看交互式仪表板")
        print(f"📋 查看 {output_dir}/analysis_report.md 了解详细分析")

    except Exception as e:
        print(f"❌ 生成可视化时出错: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
