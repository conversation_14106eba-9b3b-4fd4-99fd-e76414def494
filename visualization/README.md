# 移动智能体评估结果可视化系统

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 运行可视化
```bash
python run_visualization.py
```

### 3. 查看结果
生成的文件保存在 `visualization/output/` 目录中。

## 📁 文件结构

```
visualization/
├── dashboard.py              # 核心可视化引擎
├── README.md                # 本说明文档
└── output/                  # 输出目录
    ├── radar_chart.png      # 雷达图
    ├── radar_chart.html     # 交互式雷达图
    ├── distribution_plots.png # 分布图
    ├── performance_heatmap.png # 热力图
    ├── trend_analysis.png   # 趋势图
    ├── trend_analysis.html  # 交互式趋势图
    ├── interactive_dashboard.html # 综合仪表板
    └── analysis_report.md   # 分析报告
```