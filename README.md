# Mobile Agent Evaluation System

移动智能体评估系统 - 基于LLM的智能体行为评估框架

## 🎯 项目概述

本项目是一个完整的移动智能体评估系统，专门用于评估智能体在移动应用操作任务中的表现。系统通过对比智能体与人类的操作行为，使用大语言模型进行多维度量化评估，生成详细的评估报告和统计分析。

**核心特性：**
- 🤖 **智能体行为分析**：深度解析移动应用操作轨迹
- 📊 **多维度评估**：任务完成度、过程正确性、相对效率、策略一致性
- 🧠 **LLM驱动评估**：基于大语言模型的智能评判
- 📈 **完整评估流程**：从数据提取到结果分析的端到端解决方案

## 🏗️ 系统架构

```
Mobile Agent Evaluation System
├── 🚀 主控制器 (runner.py)        # 评估流程总控制
├── 📊 数据提取层 (extractor/)      # 原始数据解析处理
├── ⚙️ 配置管理层 (config/)        # 系统配置与环境管理
├── 🧠 评估引擎层 (evaluator/)     # LLM评估核心逻辑
└── 📁 数据存储层 (data/)          # 输入数据与结果存储
```

## 📁 核心代码结构

```
mobile_agent_eval/
├── runner.py                      # 🚀 主程序入口
├── config/
│   ├── __init__.py
│   └── eval_config.py             # ⚙️ 系统配置管理
├── evaluator/
│   ├── __init__.py
│   └── core.py                    # 🧠 评估引擎核心
├── extractor/
│   ├── __init__.py
│   ├── trace_parser.py            # 📊 Fornax数据解析器
│   └── cagui_parser.py            # 📊 CAGUI数据解析器
├── data/
│   ├── raw_traces/                # 原始trace数据
│   │   └── fornax_raw_trace.csv
│   └── groudtruth/               # 人类基准数据
│       └── gt.csv
└── requirements.txt               # 依赖包列表
```

## 🔧 核心组件详解

### 1. 主控制器 (`runner.py`)

系统的核心入口，协调整个评估流程：

**主要功能：**
- 🔄 **三阶段流程控制**：数据提取 → 系统配置 → 执行评估
- 📝 **完整日志记录**：支持控制台和文件双重日志输出
- ⚡ **环境验证**：自动检查API密钥和输入文件
- 🛡️ **错误处理**：优雅的异常处理和错误恢复

**核心流程：**
```python
# 第一阶段：数据提取
agent_csv_path = extract_agent_data(args.input, args.output)

# 第二阶段：配置评估系统
config = EvalConfig(...)

# 第三阶段：执行评估
results = run_evaluation(config, agent_csv_path, args.human_csv, results_path)
```

### 2. 配置管理 (`config/eval_config.py`)

统一的配置管理系统：

**核心特性：**
- 🔧 **多LLM支持**：OpenAI、Azure OpenAI、OpenRouter
- 🌍 **环境变量集成**：自动从环境变量加载配置
- 📂 **路径管理**：统一管理输入输出路径
- 🏷️ **标签规则**：可配置的评估标签规则

### 3. 数据提取器 (`extractor/`)

智能体行为数据解析引擎：

**trace_parser.py** - Fornax数据解析器：
- 📋 **JSON解析**：从原始trace数据中提取结构化信息
- 🔍 **动作提取**：识别和解析智能体的操作步骤
- 📊 **元数据增强**：添加应用分类、步骤分级等维度信息
- 🛡️ **容错处理**：处理缺失数据和格式错误

**cagui_parser.py** - CAGUI数据解析器：
- 📱 **CAGUI数据集支持**：解析CAGUI格式的人类操作数据
- 🔗 **数据集下载**：https://huggingface.co/datasets/openbmb/CAGUI

### 4. 评估引擎 (`evaluator/core.py`)

基于LLM的智能评估系统：

**评估维度：**
- 🎯 **任务完成度 (Su)**：0/1分，评估是否完全达成目标
- ⚡ **过程正确性 (PS)**：0-1分，评估操作步骤的有效性
- 🚀 **相对效率 (RE)**：评估与人类操作的效率对比
- 🎨 **策略一致性 (CR)**：0-1分，评估与人类策略的重合度

## 🚀 快速开始

### 1. 环境准备

```bash
# 创建虚拟环境
uv venv
source .venv/bin/activate

# 或使用传统方式
python -m venv .venv
source .venv/bin/activate  # Linux/Mac
# .venv\Scripts\activate   # Windows

# 安装依赖
pip install -r requirements.txt
```

### 2. 环境变量配置

```bash
# 必需：设置OpenAI API密钥
export OPENAI_API_KEY="your-openai-api-key"

# 可选：自定义模型和提供商
export OPENAI_MODEL="gpt-4o-mini"        # 默认: gpt-4o-mini
export LLM_PROVIDER="openai"             # 支持: openai/azure/openrouter
export OPENAI_API_BASE="your-api-base"   # 自定义API端点(可选)
```

### 3. 数据准备

确保以下数据文件存在：
```
data/
├── raw_traces/
│   └── fornax_raw_trace.csv      # 智能体原始trace数据
└── groudtruth/
    └── gt.csv                    # 人类基准数据
```

**数据格式要求：**

**fornax_raw_trace.csv** (智能体数据)：
- `id`: 任务唯一标识
- `instruction`: 用户指令
- `output`: JSON格式的trace数据
- `应用分类`: 应用类别 (可选)
- `步骤分级`: 复杂度等级 (可选)

**gt.csv** (人类基准数据)：
- `id`: 任务唯一标识
- `instruction`: 用户指令
- `action_trace`: 人类操作步骤列表
- `expected_result`: 预期结果
- `human_steps`: 人类操作步骤数

### 4. 运行评估

```bash
# 基础运行 - 使用默认参数
python runner.py

# 自定义参数运行
python runner.py \
    --input data/raw_traces/your_trace.csv \
    --output results \
    --human-csv data/groudtruth/your_gt.csv

# 查看帮助信息
python runner.py --help
```

**命令行参数：**
- `--input`: 智能体原始trace数据文件路径
- `--output`: 结果输出目录
- `--human-csv`: 人类基准数据文件路径

## 📊 输出结果

系统运行完成后会生成以下输出：

```
results/
├── extracted_traces/
│   └── agent_trace_MMDD_HHMM.csv    # 提取的智能体数据
├── evaluation_results.csv           # 详细评估结果
└── logs/
    └── eval_YYYYMMDD_HHMMSS.log    # 完整运行日志
```

### 评估结果文件 (evaluation_results.csv)

包含以下关键字段：
- `instruction`: 用户指令
- `human_id`: 人类记录ID
- `agent_id`: 智能体记录ID
- `Su`: 任务完成度 (0/1)
- `PS`: 过程正确性 (0-1)
- `RE`: 相对效率 (数值)
- `CR`: 策略一致性 (0-1)
- `evaluate_reason`: 详细评估理由

### 运行日志示例

```
2025-06-18 22:05:29 - mobile_agent_eval - INFO - 🚀 Mobile Agent Evaluation System 启动
2025-06-18 22:05:29 - mobile_agent_eval - INFO - 📊 第一阶段：数据提取
2025-06-18 22:05:29 - mobile_agent_eval - INFO - ✅ 数据提取完成，共 51 条记录
2025-06-18 22:05:29 - mobile_agent_eval - INFO - 🎯 第三阶段：执行评估
2025-06-18 22:05:29 - mobile_agent_eval - INFO - ✅ 评估完成，共处理 1 条记录
2025-06-18 22:05:29 - mobile_agent_eval - INFO - 📊 任务成功率 (Su): 100.0%
2025-06-18 22:05:29 - mobile_agent_eval - INFO - 🎉 评估系统运行完成！
```

## 🔧 系统特性

### 高级日志系统
- 📝 **分级日志**：DEBUG、INFO、WARNING、ERROR四个级别
- 📁 **文件轮转**：自动管理日志文件大小，支持历史备份
- 🖥️ **双重输出**：控制台实时显示 + 文件详细记录
- 🕒 **时间戳**：精确到秒的时间记录

### 智能错误处理
- 🛡️ **数据容错**：自动处理缺失和格式错误的数据
- 🔄 **自动重试**：LLM调用失败时的智能重试机制
- 📊 **统计保护**：数值转换错误的安全处理
- ⚡ **优雅降级**：部分失败时继续处理其他数据

### 多LLM支持
- 🤖 **OpenAI**：标准OpenAI API，支持GPT系列模型
- ☁️ **Azure OpenAI**：企业级Azure OpenAI服务
- 🌐 **OpenRouter**：多模型聚合服务，支持更多模型选择
- 🔧 **灵活配置**：通过环境变量轻松切换LLM提供商

## 📋 依赖要求

```        
tqdm>=4.64.0           
fastapi==0.115.12
pandas==2.3.0
openai==0.28.0
uvicorn==0.34.2
pydantic==2.11.4
tomli==2.2.1
dotenv==0.9.9
# 可视化依赖
matplotlib>=3.5.0
seaborn>=0.11.0
plotly>=5.0.0
numpy>=1.21.0
```

**Python版本要求：** Python 3.8+





## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

---




