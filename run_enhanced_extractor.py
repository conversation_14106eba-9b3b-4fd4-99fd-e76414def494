#!/usr/bin/env python3
"""
增强版数据提取器运行脚本
从fornax_raw_trace.csv提取数据，包含id、应用分类、步骤分级等额外信息
支持CSV和JSONL两种输出格式
"""
import sys
import os
from datetime import datetime

# 添加extractor目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'extractor'))

def main():
    """主函数"""
    print("🚀 启动增强版数据提取器...")
    print("=" * 60)
    
    try:
        from trace_parser import extract_all_from_csv
        
        # 配置文件路径
        input_csv = "data/raw_traces/fornax_raw_trace.csv"
        timestamp = datetime.now().strftime("%m%d_%H%M")
        output_base = f"data/extracted_traces/enhanced_agent_trace_{timestamp}"
        
        # 检查输入文件是否存在
        if not os.path.exists(input_csv):
            print(f"❌ 输入文件不存在: {input_csv}")
            print("请确保fornax_raw_trace.csv文件在正确位置")
            return
        
        print(f"📂 输入文件: {input_csv}")
        print(f"📤 输出基础路径: {output_base}")
        
        # 提取数据并保存为多种格式
        print("\n🔄 开始提取数据...")
        df_metrics, saved_files = extract_all_from_csv(
            csv_path=input_csv, 
            save_path=output_base, 
            output_format='both'  # 同时输出CSV和JSONL
        )
        
        # 显示结果统计
        print(f"\n📊 提取完成！")
        print(f"✅ 成功提取 {len(df_metrics)} 条记录")
        
        print("\n📄 生成的文件:")
        for file_path in saved_files:
            file_size = os.path.getsize(file_path) / 1024  # KB
            print(f"  📁 {file_path} ({file_size:.1f} KB)")
        
        # 显示数据预览
        print("\n🔍 数据预览 (前3条记录):")
        print("-" * 60)
        for i, (_, row) in enumerate(df_metrics.head(3).iterrows()):
            print(f"记录 {i+1}:")
            print(f"  ID: {row.get('id', 'N/A')}")
            print(f"  指令: {row.get('instruction', 'N/A')[:50]}...")
            print(f"  应用分类: {row.get('应用分类', 'N/A')}")
            print(f"  步骤分级: {row.get('步骤分级', 'N/A')}")
            print(f"  步骤数: {row.get('num_steps', 'N/A')}")
            print(f"  是否成功: {row.get('is_success', 'N/A')}")
            print()
        
        # 显示列信息
        print("📋 数据结构信息:")
        print(f"  总列数: {len(df_metrics.columns)}")
        print("  列名:", ", ".join(df_metrics.columns))
        
        # 显示统计信息
        if '应用分类' in df_metrics.columns:
            print(f"\n📈 应用分类分布:")
            app_counts = df_metrics['应用分类'].value_counts()
            for app, count in app_counts.head(5).items():
                print(f"  {app}: {count} 条")
        
        if '步骤分级' in df_metrics.columns:
            print(f"\n📈 步骤分级分布:")
            step_counts = df_metrics['步骤分级'].value_counts()
            for level, count in step_counts.items():
                print(f"  {level}: {count} 条")
        
        if 'is_success' in df_metrics.columns:
            success_rate = df_metrics['is_success'].mean() * 100
            print(f"\n📈 任务成功率: {success_rate:.1f}%")
        
        print("\n🎉 数据提取完成！")
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保trace_parser.py文件存在且可访问")
    except Exception as e:
        print(f"❌ 提取失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
