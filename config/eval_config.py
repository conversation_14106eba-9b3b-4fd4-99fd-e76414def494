import os
import dotenv
from dataclasses import dataclass, field
from pathlib import Path
from typing import Optional

dotenv.load_dotenv()

@dataclass
class EvalConfig:
    """统一的评测系统配置类"""
    # 路径配置
    raw_traces_dir: Path = Path("data/raw_traces")
    extracted_traces_dir: Path = Path("data/extracted_traces")
    
    # 评测配置
    metrics: list[str] = field(default_factory=list)
    label_rules: dict = field(default_factory=dict)
    
    # LLM配置
    llm_provider: str = "openai"
    openai_api_key: Optional[str] = None
    openai_model: Optional[str] = None
    openai_api_base: Optional[str] = None
    openai_api_version: Optional[str] = None
    openrouter_api_key: Optional[str] = None
    openrouter_api_base: Optional[str] = None
    openrouter_model: Optional[str] = None

    # 方舟ARK豆包模型配置
    ark_api_key: Optional[str] = None
    ark_api_base: Optional[str] = None
    ark_model: Optional[str] = None
    
    def __post_init__(self):
        # 确保加载.env文件
        dotenv.load_dotenv()

        # 初始化标签规则
        self.label_rules = self.label_rules or {
            "success": ["任务完成", "操作成功"],
            "failure": ["任务失败", "操作未完成"]
        }

        # 统一从环境变量加载所有LLM配置
        self.llm_provider = os.getenv("LLM_PROVIDER", "openai").lower()

        # OpenAI配置
        if not self.openai_api_key:
            self.openai_api_key = os.getenv("OPENAI_API_KEY")
        if not self.openai_model:
            self.openai_model = os.getenv("OPENAI_MODEL", "gpt-4o-mini")
        if not self.openai_api_base:
            self.openai_api_base = os.getenv("OPENAI_API_BASE")
        if not self.openai_api_version:
            self.openai_api_version = os.getenv("OPENAI_API_VERSION")

        # OpenRouter配置
        if not self.openrouter_api_key:
            self.openrouter_api_key = os.getenv("OPENROUTER_API_KEY")
        if not self.openrouter_api_base:
            self.openrouter_api_base = os.getenv("OPENROUTER_API_BASE")
        if not self.openrouter_model:
            self.openrouter_model = os.getenv("OPENROUTER_MODEL")

        # ARK配置
        if not self.ark_api_key:
            self.ark_api_key = os.getenv("ARK_API_KEY")
        if not self.ark_api_base:
            self.ark_api_base = os.getenv("ARK_API_BASE", "https://ark.cn-beijing.volces.com/api/v3")
        if not self.ark_model:
            self.ark_model = os.getenv("ARK_MODEL", "doubao-seed-1-6-250615")


