# LLM标签系统任务

## 上下文
用户需要基于human_trace_0616_1511数据，使用OpenAI API进行智能标签分析，为每条instruction添加三个维度的标签。

## 计划概要
1. 创建OpenAI API标签分析器
2. 设计分析prompt（步骤分级、应用分类、身份验证）
3. 实现批量处理脚本
4. 输出增强版CSV文件

## 执行状态
- [x] 安装依赖 (requirements_llm.txt已创建)
- [x] 创建配置文件 (extractor/config.py)
- [x] 实现LLM标签分析器 (extractor/llm_labeler.py) - 已优化
- [x] 创建批量处理脚本 (extractor/batch_labeler.py) - 已优化
- [x] 创建运行脚本 (run_labeler.py)
- [x] 优化使用metrics.py的LLM配置
- [x] 实现真正的批量处理 (10条/批次)
- [x] 实现专用System Prompt (针对标签分析优化)
- [ ] 执行标签处理
- [ ] 验证结果

## 任务1创建的文件
1. extractor/config.py - 配置文件
2. extractor/llm_labeler.py - 核心标签分析器 (已优化)
3. extractor/batch_labeler.py - 批量处理脚本 (已优化)
4. run_labeler.py - 主运行脚本
5. test_labeler.py - 测试脚本 (已优化)
6. requirements_llm.txt - 依赖列表
7. extractor/README_LLM_LABELER.md - 使用说明
8. LLM_LABELER_GUIDE.md - 完整使用指南 (已更新)

## 任务2创建/修改的文件
1. extractor/trace_parser.py - 增强版数据提取器 (已修改)
2. run_enhanced_extractor.py - 增强版提取器运行脚本
3. test_enhanced_extractor.py - 增强版提取器测试脚本
4. ENHANCED_EXTRACTOR_GUIDE.md - 增强版提取器使用指南

## 任务1完成情况
- ✅ 使用metrics.py的LLMConfig和Evaluator
- ✅ 支持多种LLM提供商 (OpenAI/Azure/OpenRouter)
- ✅ 实现真正的批量处理 (10条记录/批次)
- ✅ 智能降级处理 (批量失败时自动单条处理)
- ✅ 专用System Prompt (针对标签分析任务优化)
- ✅ 性能提升约10倍 (减少API调用次数)

## 任务2完成情况
- ✅ 修改extract_metrics_from_trace函数支持额外字段
- ✅ 从fornax_raw_trace.csv提取id、应用分类、步骤分级
- ✅ 添加save_as_jsonl函数支持JSONL格式输出
- ✅ 更新extract_all_from_csv支持多格式输出
- ✅ 创建run_enhanced_extractor.py运行脚本
- ✅ 创建test_enhanced_extractor.py测试脚本
- ✅ 编写完整的使用指南文档
