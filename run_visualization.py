#!/usr/bin/env python3
"""
"""
import sys
import os
from pathlib import Path

# 添加visualization目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "visualization"))

def main():
    """主函数"""
    print("🎨 智能体评估结果可视化")
    print("=" * 50)
    
    # 默认CSV文件路径
    default_csv = "results/multiagent_uitars_results.csv"
    
    # 检查默认文件是否存在
    if Path(default_csv).exists():
        csv_path = default_csv
        print(f"📊 使用默认数据文件: {csv_path}")
    else:
        print(f"❌ 默认文件不存在: {default_csv}")
        print("请指定CSV文件路径:")
        csv_path = input("CSV文件路径: ").strip()
        
        if not csv_path or not Path(csv_path).exists():
            print("❌ 文件路径无效，退出程序")
            sys.exit(1)
    
    try:
        # 检查依赖
        print("\n🔍 检查依赖...")
        missing_deps = []
        
        try:
            import matplotlib
            print("✅ matplotlib")
        except ImportError:
            missing_deps.append("matplotlib")
            
        try:
            import seaborn
            print("✅ seaborn")
        except ImportError:
            missing_deps.append("seaborn")
            
        try:
            import plotly
            print("✅ plotly")
        except ImportError:
            missing_deps.append("plotly")
            
        try:
            import numpy
            print("✅ numpy")
        except ImportError:
            missing_deps.append("numpy")
        
        if missing_deps:
            print(f"\n❌ 缺少依赖: {', '.join(missing_deps)}")
            print("请运行: pip install -r requirements.txt")
            sys.exit(1)
        
        # 导入并运行可视化
        from dashboard import EvaluationDashboard
        
        print(f"\n🚀 开始生成可视化报告...")
        dashboard = EvaluationDashboard(csv_path)
        output_dir = dashboard.generate_all_visualizations()
        
        print(f"\n🎉 可视化报告生成完成！")
        print(f"📁 输出目录: {output_dir}")
        print("\n📊 生成的文件包括:")
        print("  🎯 radar_chart.png - 四维度雷达图")
        print("  📈 distribution_plots.png - 指标分布图")
        print("  🔥 performance_heatmap.png - 性能热力图")
        print("  📊 trend_analysis.png - 趋势分析图")
        print("  🌐 interactive_dashboard.html - 交互式仪表板")
        print("  📋 analysis_report.md - 详细分析报告")
        
        print(f"\n💡 建议:")
        print(f"  1. 打开 {output_dir}/interactive_dashboard.html 查看交互式图表")
        print(f"  2. 查看 {output_dir}/analysis_report.md 了解数据洞察")

    except Exception as e:
        print(f"\n❌ 生成可视化时出错: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
