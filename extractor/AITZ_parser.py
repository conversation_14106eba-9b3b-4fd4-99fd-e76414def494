# coding=utf-8
import os
import json
from enum import IntEnum
from datetime import datetime

import pandas as pd
import imagesize
import numpy as np

current_dir = os.path.dirname(os.path.abspath(__file__))

# —— 1. 定义动作类型（复用第一段中的 ActionType） ———————————————
class ActionType(IntEnum):
    UNUSED_0 = 0
    UNUSED_1 = 1
    UNUSED_2 = 2
    TYPE = 3
    DUAL_POINT = 4
    PRESS_BACK = 5
    PRESS_HOME = 6
    PRESS_ENTER = 7
    UNUSED_8 = 8
    UNUSED_9 = 9
    STATUS_TASK_COMPLETE = 10
    STATUS_TASK_IMPOSSIBLE = 11

# —— 2. Tap/Swipe 判定 ——（
_TAP_THRESHOLD = 0.04  # normalized distance threshold

def is_tap_action(start, end, threshold=_TAP_THRESHOLD):
    dy = start[0] - end[0]
    dx = start[1] - end[1]
    return (dy*dy + dx*dx) ** 0.5 <= threshold

# —— 3. 辅助：从归一化坐标到像素 bbox ——  
def to_bbox_str(yx_norm, image_path, delta_px=10):
    """
    返回 <bbox>left top right bottom</bbox> 格式字符串
    """
    w, h = imagesize.get(image_path)
    y, x = yx_norm
    px = int(x * w)
    py = int(y * h)
    left, top = px - delta_px, py - delta_px
    right, bottom = px + delta_px, py + delta_px
    # 保证不出界
    left, top = max(0, left), max(0, top)
    right, bottom = min(w, right), min(h, bottom)
    return f"<bbox>{left} {top} {right} {bottom}</bbox>"

 
def extract_human_action(step):
    """
    输入一条 step（dict），返回像 'click(start_box=...)' 这样的字符串
    """
    atype = ActionType(step['result_action_type'])
    img_path = step.get('image_full_path') or step.get('image_path')
    touch = json.loads(step.get('result_touch_yx', '[0,0]'))
    lift  = json.loads(step.get('result_lift_yx', '[0,0]'))
    
    if atype == ActionType.DUAL_POINT:
        # tap or drag
        if is_tap_action(touch, lift):
            bbox = to_bbox_str(lift, img_path)
            return f"click(start_box='{bbox}')"
        else:
            bbox1 = to_bbox_str(touch, img_path)
            bbox2 = to_bbox_str(lift, img_path)
            return f"drag(start_box='{bbox1}', end_box='{bbox2}')"
    
    elif atype == ActionType.TYPE:
        txt = step.get('result_action_text', '').replace("'", "\\'")
        return f"type(content='{txt}')"
    
    elif atype == ActionType.PRESS_BACK:
        return "press_back()"
    elif atype == ActionType.PRESS_HOME:
        return "press_home()"
    elif atype == ActionType.PRESS_ENTER:
        return "press_enter()"
    
    elif atype == ActionType.STATUS_TASK_COMPLETE:
        return "finished(content='任务完成')"
    elif atype == ActionType.STATUS_TASK_IMPOSSIBLE:
        return "unfinished(content='任务失败')"
    
    else:
        # NO_ACTION, UNUSED_xxx …
        return "wait()"

def collect_human_traces(root_dir):
    results = []
    trace_id = 1

    for subdir, _, files in os.walk(root_dir):
        for fname in files:
            if not fname.endswith('.json'):
                continue
            path = os.path.join(subdir, fname)
            with open(path, 'r', encoding='utf-8') as f:
                try:
                    steps = json.load(f)
                except json.JSONDecodeError:
                    continue
            if not steps:
                continue

            episode_id = steps[0].get('episode_id')
            instruction = steps[0].get('instruction', '')
            actions = [extract_human_action(step) for step in steps]
            
            final_img = steps[-1].get('image_path') or steps[-1].get('image_full_path')
            final_action_result = steps[-1].get('coat_action_result', '')

            results.append({
                "id": trace_id,
                "episode_id": episode_id,
                "instruction": instruction,
                "action_trace": actions,
                "human_steps": len(actions),
                "expected_result": f"完成了“{instruction}”这个任务",
                "final_image_path": final_img,
                "final_action_result": final_action_result
            })
            trace_id += 1

    return results

 
def save_as_jsonl(data, path):
    with open(path, 'w', encoding='utf-8') as f:
        for item in data:
            f.write(json.dumps(item, ensure_ascii=False) + "\n")

def save_as_csv(data, path):
    df = pd.DataFrame(data)
    df.to_csv(path, index=False, encoding='utf-8-sig')
    
if __name__ == "__main__":
    INPUT_DIR = "/Users/<USER>/Documents/aitz/android_in_the_zoo/test/general"   # <<< 修改为你的根目录
    timestamp = datetime.now().strftime("%m%d_%H%M")
    out_jsonl = f".AITZ/human_trace_{timestamp}.jsonl"
    out_csv   = f"./AITZ/human_trace_{timestamp}.csv"

    traces = collect_human_traces(INPUT_DIR)
    save_as_jsonl(traces, out_jsonl)
    save_as_csv(traces, out_csv)

    print(f"✅ 完成，共提取 {len(traces)} 条 human trace")
    print(f"📄 JSONL 输出: {out_jsonl}")
    print(f"📄 CSV 输出: {out_csv}")
