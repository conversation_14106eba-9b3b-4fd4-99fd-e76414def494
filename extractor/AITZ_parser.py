# coding=utf-8
import os
import json
from enum import IntEnum
from datetime import datetime

import pandas as pd
import imagesize
import numpy as np

current_dir = os.path.dirname(os.path.abspath(__file__))

# —— 1. 定义动作类型（复用第一段中的 ActionType） ———————————————
class ActionType(IntEnum):
    UNUSED_0 = 0
    UNUSED_1 = 1
    UNUSED_2 = 2
    TYPE = 3
    DUAL_POINT = 4
    PRESS_BACK = 5
    PRESS_HOME = 6
    PRESS_ENTER = 7
    UNUSED_8 = 8
    UNUSED_9 = 9
    STATUS_TASK_COMPLETE = 10
    STATUS_TASK_IMPOSSIBLE = 11

# —— 2. Tap/Swipe 判定 ——（
_TAP_THRESHOLD = 0.04  # normalized distance threshold

def is_tap_action(start, end, threshold=_TAP_THRESHOLD):
    dy = start[0] - end[0]
    dx = start[1] - end[1]
    return (dy*dy + dx*dx) ** 0.5 <= threshold

# —— 3. 辅助：从归一化坐标到像素 bbox ——  
def to_bbox_str(yx_norm, image_path, delta_px=10):
    """
    返回 <bbox>left top right bottom</bbox> 格式字符串
    """
    # 处理路径问题 - 如果是完整路径但文件不存在，尝试使用相对路径
    if not os.path.exists(image_path):
        # 提取文件名，在data目录下查找
        filename = os.path.basename(image_path)
        # 从路径中提取episode_id
        if 'GENERAL-' in image_path:
            episode_id = image_path.split('/')[-2] if '/' in image_path else image_path.split('\\')[-2]
            relative_path = f"data/general/{episode_id}/{filename}"
            if os.path.exists(relative_path):
                image_path = relative_path

    try:
        w, h = imagesize.get(image_path)
    except (FileNotFoundError, OSError):
        # 如果图片文件不存在，使用默认尺寸
        w, h = 1080, 1920  # 默认Android屏幕尺寸

    y, x = yx_norm
    px = int(x * w)
    py = int(y * h)
    left, top = px - delta_px, py - delta_px
    right, bottom = px + delta_px, py + delta_px
    # 保证不出界
    left, top = max(0, left), max(0, top)
    right, bottom = min(w, right), min(h, bottom)
    return f"<bbox>{left} {top} {right} {bottom}</bbox>"

 
def extract_human_action(step):
    """
    输入一条 step（dict），返回像 'click(start_box=...)' 这样的字符串
    """
    atype = ActionType(step['result_action_type'])
    img_path = step.get('image_full_path') or step.get('image_path')
    touch = json.loads(step.get('result_touch_yx', '[0,0]'))
    lift  = json.loads(step.get('result_lift_yx', '[0,0]'))
    
    if atype == ActionType.DUAL_POINT:
        # tap or drag
        if is_tap_action(touch, lift):
            bbox = to_bbox_str(lift, img_path)
            return f"click(start_box='{bbox}')"
        else:
            bbox1 = to_bbox_str(touch, img_path)
            bbox2 = to_bbox_str(lift, img_path)
            return f"drag(start_box='{bbox1}', end_box='{bbox2}')"
    
    elif atype == ActionType.TYPE:
        txt = step.get('result_action_text', '').replace("'", "\\'")
        return f"type(content='{txt}')"
    
    elif atype == ActionType.PRESS_BACK:
        return "press_back()"
    elif atype == ActionType.PRESS_HOME:
        return "press_home()"
    elif atype == ActionType.PRESS_ENTER:
        return "press_enter()"
    
    elif atype == ActionType.STATUS_TASK_COMPLETE:
        return "finished(content='任务完成')"
    elif atype == ActionType.STATUS_TASK_IMPOSSIBLE:
        return "unfinished(content='任务失败')"
    
    else:
        # NO_ACTION, UNUSED_xxx …
        return "wait()"

def collect_human_traces(root_dir):
    results = []
    trace_id = 1

    for subdir, _, files in os.walk(root_dir):
        for fname in files:
            if not fname.endswith('.json'):
                continue
            path = os.path.join(subdir, fname)
            with open(path, 'r', encoding='utf-8') as f:
                try:
                    steps = json.load(f)
                except json.JSONDecodeError:
                    continue
            if not steps:
                continue

            episode_id = steps[0].get('episode_id')
            instruction = steps[0].get('instruction', '')
            actions = [extract_human_action(step) for step in steps]
            
            final_img = steps[-1].get('image_path') or steps[-1].get('image_full_path')
            final_action_result = steps[-1].get('coat_action_result', '')

            # 提取新增字段 - 收集所有步骤的相关信息
            coat_action_thinks = []
            coat_action_descs = []
            coat_action_results = []
            coat_screen_descs = []
            result_touch_coords = []
            result_lift_coords = []
            action_details = []  # 新增：动作详情（关联action和坐标）

            for i, step in enumerate(steps):
                # 提取思考过程
                coat_action_thinks.append(step.get('coat_action_think', ''))
                # 提取动作描述
                coat_action_descs.append(step.get('coat_action_desc', ''))
                # 提取动作结果
                coat_action_results.append(step.get('coat_action_result', ''))
                # 提取屏幕描述
                coat_screen_descs.append(step.get('coat_screen_desc', ''))

                # 提取触摸坐标（归一化值）
                touch_yx = step.get('result_touch_yx', '[-1.0, -1.0]')
                lift_yx = step.get('result_lift_yx', '[-1.0, -1.0]')

                try:
                    touch_coords = json.loads(touch_yx) if touch_yx else [-1.0, -1.0]
                    lift_coords = json.loads(lift_yx) if lift_yx else [-1.0, -1.0]
                except (json.JSONDecodeError, TypeError):
                    touch_coords = [-1.0, -1.0]
                    lift_coords = [-1.0, -1.0]

                result_touch_coords.append(touch_coords)
                result_lift_coords.append(lift_coords)

                # 创建动作详情（关联action和坐标信息）
                action_detail = {
                    "step_id": i,
                    "action": actions[i],
                    "touch_yx": touch_coords,
                    "lift_yx": lift_coords,
                    "action_type": step.get('result_action_type', -1),
                    "action_text": step.get('result_action_text', ''),
                    "think": step.get('coat_action_think', ''),
                    "desc": step.get('coat_action_desc', ''),
                    "result": step.get('coat_action_result', '')
                }
                action_details.append(action_detail)

            # 使用最后一步的coat_action_result作为expected_result
            expected_result = steps[-1].get('coat_action_result')

            results.append({
                "id": trace_id,
                "episode_id": episode_id,
                "instruction": instruction,
                "action_trace": actions,
                "human_steps": len(actions),
                "expected_result": expected_result,
                "final_image_path": final_img,
                "final_action_result": final_action_result,
                # 新增字段
                "coat_action_think": coat_action_thinks,
                "coat_action_desc": coat_action_descs,
                "coat_action_result": coat_action_results,
                "coat_screen_desc": coat_screen_descs,
                "result_touch_yx": result_touch_coords,
                "result_lift_yx": result_lift_coords,
                # 动作详情（关联action和坐标）
                "action_details": action_details
            })
            trace_id += 1

    return results

 
def save_as_jsonl(data, path):
    with open(path, 'w', encoding='utf-8') as f:
        for item in data:
            f.write(json.dumps(item, ensure_ascii=False) + "\n")

def save_as_csv(data, path):
    df = pd.DataFrame(data)
    df.to_csv(path, index=False, encoding='utf-8-sig')
    
if __name__ == "__main__":
    INPUT_DIR = "/Users/<USER>/Documents/aitz/android_in_the_zoo/test/general"   # <<< 修改为你的根目录
    timestamp = datetime.now().strftime("%m%d_%H%M")
    out_jsonl = f".AITZ/human_trace_{timestamp}.jsonl"
    out_csv   = f"./AITZ/human_trace_{timestamp}.csv"

    traces = collect_human_traces(INPUT_DIR)
    save_as_jsonl(traces, out_jsonl)
    save_as_csv(traces, out_csv)

    print(f"✅ 完成，共提取 {len(traces)} 条 human trace")
    print(f"📄 JSONL 输出: {out_jsonl}")
    print(f"📄 CSV 输出: {out_csv}")
