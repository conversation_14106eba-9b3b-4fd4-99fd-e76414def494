"""
LLM标签分析器 - 使用metrics.py的LLM配置进行智能标签分析
"""
import json
import time
import sys
import os
from typing import Dict, List

# 添加evaluator目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'evaluator'))
# sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'evaluator'))

from metrics import LLMConfig, Evaluator
from config import (
    APP_CATEGORIES,
    MAX_RETRIES, RETRY_DELAY
)

class LLMLabeler:
    def __init__(self):
        """初始化LLM标签分析器"""
        try:
            # 使用metrics.py的LLM配置
            self.llm_config = LLMConfig()
            self.evaluator = Evaluator(self.llm_config)
        except Exception as e:
            raise ValueError(f"初始化LLM配置失败: {e}")

        print(f"✅ 使用LLM提供商: {self.llm_config.provider}")
        print(f"✅ 使用模型: {self.llm_config.model}")
        
    def create_analysis_prompt(self, instruction: str, action_trace: list, human_steps: int) -> str:
        """创建分析prompt"""
        
        # 构建应用分类选项
        app_options = []
        for category, apps in APP_CATEGORIES.items():
            if apps:
                app_options.append(f"- {category}: {', '.join(apps)}")
            else:
                app_options.append(f"- {category}")
        
        prompt = f"""
请分析以下移动应用操作任务，并从三个维度进行分类：

任务描述: {instruction}
操作步骤数: {human_steps}
操作序列: {action_trace}

请按以下三个维度进行分析：

1. **步骤分级** (根据操作复杂度):
   - 简单: 1-3个操作步骤
   - 中等: 4-6个操作步骤  
   - 复杂: 7个以上操作步骤

2. **应用分类** (根据涉及的主要应用):
{chr(10).join(app_options)}

3. **身份验证** (是否需要登录或特殊权限):
   - 需要登录: 涉及个人账户操作、发送消息、购买等
   - 无需登录: 基础搜索、查看公开内容
   - 需要特殊权限: 会议加入、支付操作、系统设置

请严格按照以下JSON格式返回结果，不要包含任何其他文字：
{{
    "步骤分级": "简单/中等/复杂",
    "应用分类": "具体分类名称——具体应用名称",
    "身份验证": "需要登录/无需登录/需要特殊权限"
}}
"""
        return prompt

    def create_batch_analysis_prompt(self, batch_data: List[Dict]) -> str:
        """创建批量分析prompt"""

        # 构建应用分类选项
        app_options = []
        for category, apps in APP_CATEGORIES.items():
            if apps:
                app_options.append(f"- {category}: {', '.join(apps)}")
            else:
                app_options.append(f"- {category}")

        # 构建批量任务描述
        tasks_description = []
        for i, item in enumerate(batch_data, 1):
            tasks_description.append(f"""
任务{i}:
  指令: {item['instruction']}
  操作步骤数: {item['human_steps']}
  操作序列: {item['action_trace']}""")

        prompt = f"""
请分析以下{len(batch_data)}个移动应用操作任务，并从三个维度进行分类：

{chr(10).join(tasks_description)}

请按以下三个维度进行分析：

1. **步骤分级** (根据操作复杂度):
   - 简单: 1-3个操作步骤
   - 中等: 4-6个操作步骤
   - 复杂: 7个以上操作步骤

2. **应用分类** (根据涉及的主要应用):
{chr(10).join(app_options)}

3. **身份验证** (是否需要登录或特殊权限):
   - 需要登录: 涉及个人账户操作、发送消息、购买等
   - 无需登录: 基础搜索、查看公开内容
   - 需要特殊权限: 会议加入、支付操作、系统设置

请严格按照以下JSON数组格式返回结果，不要包含任何其他文字：
[
    {{"步骤分级": "简单/中等/复杂", "应用分类": "具体分类名称——具体应用名称", "身份验证": "需要登录/无需登录/需要特殊权限"}},
    {{"步骤分级": "简单/中等/复杂", "应用分类": "具体分类名称——具体应用名称", "身份验证": "需要登录/无需登录/需要特殊权限"}},
    ...
]
"""
        return prompt

    def analyze_instruction(self, instruction: str, action_trace: list, human_steps: int) -> Dict[str, str]:
        """分析单个instruction并返回标签"""

        prompt = self.create_analysis_prompt(instruction, action_trace, human_steps)

        for attempt in range(MAX_RETRIES):
            try:
                # 使用自定义system prompt调用LLM
                result_text = self._call_llm_with_custom_prompt(prompt)

                # 尝试解析JSON结果
                try:
                    result = json.loads(result_text)

                    # 验证结果格式
                    required_keys = ["步骤分级", "应用分类", "身份验证"]
                    if all(key in result for key in required_keys):
                        return result
                    else:
                        print(f"警告: 返回结果缺少必要字段: {result}")

                except json.JSONDecodeError:
                    print(f"警告: 无法解析JSON结果: {result_text}")

                # 如果解析失败，使用备用逻辑
                return self._fallback_analysis(instruction, human_steps)

            except Exception as e:
                print(f"API调用失败 (尝试 {attempt + 1}/{MAX_RETRIES}): {e}")
                if attempt < MAX_RETRIES - 1:
                    time.sleep(RETRY_DELAY * (attempt + 1))
                else:
                    print("所有重试都失败，使用备用分析")
                    return self._fallback_analysis(instruction, human_steps)

    def analyze_batch(self, batch_data: List[Dict]) -> List[Dict[str, str]]:
        """批量分析多个instruction并返回标签列表"""

        prompt = self.create_batch_analysis_prompt(batch_data)

        for attempt in range(MAX_RETRIES):
            try:
                # 使用自定义system prompt调用LLM
                result_text = self._call_llm_with_custom_prompt(prompt)

                # 尝试解析JSON结果
                try:
                    results = json.loads(result_text)

                    # 验证结果格式
                    if isinstance(results, list) and len(results) == len(batch_data):
                        valid_results = []
                        for i, result in enumerate(results):
                            required_keys = ["步骤分级", "应用分类", "身份验证"]
                            if all(key in result for key in required_keys):
                                valid_results.append(result)
                            else:
                                print(f"警告: 第{i+1}条记录结果缺少必要字段，使用备用分析")
                                valid_results.append(self._fallback_analysis(
                                    batch_data[i]['instruction'],
                                    batch_data[i]['human_steps']
                                ))
                        return valid_results
                    else:
                        print(f"警告: 批量结果格式错误，期望{len(batch_data)}条，实际{len(results) if isinstance(results, list) else 0}条")

                except json.JSONDecodeError:
                    print(f"警告: 无法解析批量JSON结果: {result_text[:200]}...")

                # 如果解析失败，逐个使用备用逻辑
                print("批量解析失败，使用备用分析")
                return [self._fallback_analysis(item['instruction'], item['human_steps'])
                       for item in batch_data]

            except Exception as e:
                print(f"批量API调用失败 (尝试 {attempt + 1}/{MAX_RETRIES}): {e}")
                if attempt < MAX_RETRIES - 1:
                    time.sleep(RETRY_DELAY * (attempt + 1))
                else:
                    print("所有批量重试都失败，使用备用分析")
                    return [self._fallback_analysis(item['instruction'], item['human_steps'])
                           for item in batch_data]

    def _call_llm_with_custom_prompt(self, user_prompt: str) -> str:
        """使用自定义system prompt调用LLM"""

        # 专门针对标签分析的system prompt
        system_prompt = """你是一个专业的移动应用任务分析专家，擅长对移动应用操作进行分类和标签化。

你的任务是分析移动应用操作指令，并从以下三个维度进行准确分类：
1. 步骤分级：根据操作复杂度分为简单、中等、复杂
2. 应用分类：识别涉及的主要应用类型,并列出具体的应用名称
3. 身份验证：判断是否需要登录或特殊权限

请严格按照要求的JSON格式返回分析结果，确保输出格式正确且完整。"""

        # 根据不同的LLM提供商调用相应的API
        if self.llm_config.provider == "azure":
            import openai
            response = openai.ChatCompletion.create(
                engine=self.llm_config.model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=0.1
            )
            return response.choices[0].message['content']

        elif self.llm_config.provider == "openrouter":
            import openai
            response = openai.ChatCompletion.create(
                model=self.llm_config.openrouter_model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=0.1
            )
            return response.choices[0].message['content']

        else:  # 默认使用OpenAI
            import openai
            client = openai.OpenAI(api_key=self.llm_config.api_key)
            response = client.chat.completions.create(
                model=self.llm_config.model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=0.1
            )
            return response.choices[0].message.content

    def _fallback_analysis(self, instruction: str, human_steps: int) -> Dict[str, str]:
        """备用分析逻辑（当API调用失败时使用）"""
        
        # 步骤分级
        if human_steps <= 3:
            complexity = "简单"
        elif human_steps <= 6:
            complexity = "中等"
        else:
            complexity = "复杂"
        
        # 应用分类（基于关键词匹配）
        app_category = "其他"

        for category, apps in APP_CATEGORIES.items():
            for app in apps:
                if app in instruction:
                    app_category = category
                    break
            if app_category != "其他":
                break
        
        # 身份验证（基于关键词判断）
        auth_keywords = {
            "需要登录": ["发送", "购买", "加入购物车", "分享", "转发", "点赞", "发表"],
            "需要特殊权限": ["会议", "支付", "设置"],
        }
        
        auth_requirement = "无需登录"
        for auth_type, keywords in auth_keywords.items():
            if any(keyword in instruction for keyword in keywords):
                auth_requirement = auth_type
                break
        
        return {
            "步骤分级": complexity,
            "应用分类": app_category,
            "身份验证": auth_requirement
        }
