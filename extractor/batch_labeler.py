"""
批量标签处理脚本 - 处理human_trace_0616_1511数据
"""
import pandas as pd
import json
import os
from datetime import datetime
from tqdm import tqdm
from llm_labeler import LLMLabeler

def load_data(csv_path: str) -> pd.DataFrame:
    """加载CSV数据"""
    try:
        df = pd.read_csv(csv_path)
        print(f"✅ 成功加载数据，共 {len(df)} 条记录")
        return df
    except Exception as e:
        print(f"❌ 加载数据失败: {e}")
        raise

def parse_action_trace(action_trace_str: str) -> list:
    """解析action_trace字符串为列表"""
    try:
        # 尝试直接解析JSON
        return json.loads(action_trace_str)
    except:
        try:
            # 如果失败，尝试eval（不安全但可能需要）
            return eval(action_trace_str)
        except:
            # 如果都失败，返回空列表
            print(f"警告: 无法解析action_trace: {action_trace_str[:100]}...")
            return []

def process_batch(df: pd.DataFrame, labeler: <PERSON><PERSON><PERSON><PERSON>, batch_size: int = 10) -> pd.DataFrame:
    """批量处理数据 - 真正的批量处理，一次处理多条记录"""

    # 添加新列
    df['步骤分级'] = ''
    df['应用分类'] = ''
    df['身份验证'] = ''

    total_records = len(df)
    total_batches = (total_records + batch_size - 1) // batch_size

    print(f"开始批量处理 {total_records} 条记录，分为 {total_batches} 个批次，每批 {batch_size} 条...")

    # 按批次处理
    for batch_idx in tqdm(range(total_batches), desc="批次处理进度"):
        start_idx = batch_idx * batch_size
        end_idx = min(start_idx + batch_size, total_records)

        # 准备当前批次的数据
        batch_data = []
        batch_indices = []

        for idx in range(start_idx, end_idx):
            row = df.iloc[idx]
            action_trace = parse_action_trace(row['action_trace'])

            batch_data.append({
                'instruction': row['instruction'],
                'action_trace': action_trace,
                'human_steps': row['human_steps']
            })
            batch_indices.append(idx)

        try:
            # 批量调用LLM分析
            print(f"正在处理第 {batch_idx + 1}/{total_batches} 批次 (记录 {start_idx + 1}-{end_idx})...")
            results = labeler.analyze_batch(batch_data)

            # 更新DataFrame
            for i, result in enumerate(results):
                df_idx = batch_indices[i]
                df.at[df_idx, '步骤分级'] = result['步骤分级']
                df.at[df_idx, '应用分类'] = result['应用分类']
                df.at[df_idx, '身份验证'] = result['身份验证']

            print(f"✅ 第 {batch_idx + 1} 批次处理完成")

        except Exception as e:
            print(f"❌ 处理第 {batch_idx + 1} 批次时出错: {e}")
            print("使用备用逻辑逐个处理...")

            # 批量失败时，降级为逐个处理
            for i, data in enumerate(batch_data):
                try:
                    result = labeler.analyze_instruction(
                        instruction=data['instruction'],
                        action_trace=data['action_trace'],
                        human_steps=data['human_steps']
                    )
                    df_idx = batch_indices[i]
                    df.at[df_idx, '步骤分级'] = result['步骤分级']
                    df.at[df_idx, '应用分类'] = result['应用分类']
                    df.at[df_idx, '身份验证'] = result['身份验证']
                except Exception as single_error:
                    print(f"单条记录 {batch_indices[i] + 1} 处理失败: {single_error}")
                    # 设置默认值
                    df_idx = batch_indices[i]
                    df.at[df_idx, '步骤分级'] = '中等'
                    df.at[df_idx, '应用分类'] = '其他'
                    df.at[df_idx, '身份验证'] = '无需登录'

        # 每处理几个批次就保存一次中间结果
        if (batch_idx + 1) % 3 == 0 or batch_idx == total_batches - 1:
            print(f"进行中间保存... (已处理 {end_idx} 条记录)")
            save_intermediate_result(df, end_idx)

    return df

def save_intermediate_result(df: pd.DataFrame, processed_count: int):
    """保存中间结果"""
    timestamp = datetime.now().strftime("%H%M%S")
    temp_path = f"data/groudtruth/temp_labeled_{processed_count}_{timestamp}.csv"
    df.to_csv(temp_path, index=False, encoding='utf-8')
    print(f"中间结果已保存到: {temp_path}")

def save_final_result(df: pd.DataFrame, output_path: str):
    """保存最终结果"""
    try:
        df.to_csv(output_path, index=False, encoding='utf-8')
        print(f"✅ 最终结果已保存到: {output_path}")
        
        # 显示统计信息
        print("\n📊 标签统计:")
        print("步骤分级分布:")
        print(df['步骤分级'].value_counts())
        print("\n应用分类分布:")
        print(df['应用分类'].value_counts())
        print("\n身份验证分布:")
        print(df['身份验证'].value_counts())
        
    except Exception as e:
        print(f"❌ 保存结果失败: {e}")
        raise

def main():
    """主函数"""
    # 文件路径
    input_csv = "data/groudtruth/human_trace_0616_1511.csv"
    output_csv = "data/groudtruth/human_trace_0616_1511_labeled.csv"
    
    # 检查输入文件是否存在
    if not os.path.exists(input_csv):
        print(f"❌ 输入文件不存在: {input_csv}")
        return
    
    try:
        # 初始化标签分析器
        print("🚀 初始化LLM标签分析器...")
        labeler = LLMLabeler()
        
        # 加载数据
        print("📂 加载数据...")
        df = load_data(input_csv)
        
        # 批量处理 (每批10条记录)
        print("🔄 开始批量处理...")
        labeled_df = process_batch(df, labeler, batch_size=10)
        
        # 保存结果
        print("💾 保存最终结果...")
        save_final_result(labeled_df, output_csv)
        
        print("🎉 任务完成！")
        
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
        raise

if __name__ == "__main__":
    main()
