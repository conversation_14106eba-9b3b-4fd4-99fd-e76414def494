import pandas as pd
import json

def save_as_jsonl(df, file_path):
    """将DataFrame保存为JSONL格式"""
    with open(file_path, 'w', encoding='utf-8') as f:
        for _, row in df.iterrows():
            # 将pandas Series转换为字典，并处理NaN值
            row_dict = row.to_dict()
            # 将NaN值转换为None，以便JSON序列化
            for key, value in row_dict.items():
                try:
                    if pd.isna(value):
                        row_dict[key] = None
                except:
                    # 如果无法判断是否为NA，保持原值
                    pass
            f.write(json.dumps(row_dict, ensure_ascii=False) + '\n')

def extract_metrics_from_trace(output_json_str, fornax_row=None):
    result = {
        'id': '',
        'instruction': '',
        '应用分类': '',
        '步骤分级': '',
        'num_steps': 0,
        'actions': [],
        'final_action_type': '',
        'final_action_content': '',
        'execution_feedback': '',
        'is_success': False,
        'human_texts': [],
        'ai_summaries': []
    }

    # 添加fornax_raw_trace.csv中的额外信息
    if fornax_row is not None:
        result['id'] = fornax_row.get('id', '')
        result['应用分类'] = fornax_row.get('应用分类', '')
        result['步骤分级'] = fornax_row.get('步骤分级', '')
        result['instruction'] = fornax_row.get('instruction', '')

    try:
        trace = json.loads(output_json_str)

        # result['instruction'] = trace.get('user_prompt', '')
        result['execution_feedback'] = trace.get('tool_output', {}).get('result', '')
        result['final_action_type'] = trace.get('tool_call', {}).get('name', '')
        result['final_action_content'] = trace.get('tool_call', {}).get('arguments', {}).get('content', '')
        result['is_success'] = result['final_action_type'] == 'finished'

        messages = trace.get('messages', [])
        for msg in messages:
            role = msg.get('role', '')
            content = msg.get('content', '')

            if role == 'ai' and "Action:" in content:
                if "Summary:" in content:
                    summary = content.split("Action:")[0].replace("Summary:", "").strip()
                    result['ai_summaries'].append(summary)
                action_line = content.split("Action:")[-1].strip()
                result['actions'].append(action_line)

            elif role == 'human':
                # 收集所有 text 类型
                parts = msg.get('parts', [])
                for p in parts:
                    if p.get("type") == "text":
                        result['human_texts'].append(p.get("text", "").strip())

        result['num_steps'] = len(result['actions'])

    except Exception as e:
        print(f"[Error parsing trace] {e}")
        return None

    return result

def extract_all_from_csv(csv_path, save_path=None, output_format='csv'):
    """
    从CSV文件提取数据并支持多种输出格式

    Args:
        csv_path: 输入CSV文件路径
        save_path: 输出文件路径（不含扩展名）
        output_format: 输出格式，支持 'csv', 'jsonl', 'both'
    """
    df = pd.read_csv(csv_path)
    output_rows = []

    for idx, row in df.iterrows():
        # 将整行数据传递给extract_metrics_from_trace
        output_data = extract_metrics_from_trace(row['output'], fornax_row=row)
        if output_data:
            output_rows.append(output_data)

    output_df = pd.DataFrame(output_rows)

    # 根据指定格式保存文件
    saved_files = []
    if save_path:
        if output_format in ['csv', 'both']:
            csv_path = f"{save_path}.csv"
            output_df.to_csv(csv_path, index=False, encoding='utf-8')
            saved_files.append(csv_path)
            print(f"[✓] CSV结果保存至 {csv_path}")

        # if output_format in ['jsonl', 'both']:
        #     jsonl_path = f"{save_path}.jsonl"
        #     save_as_jsonl(output_df, jsonl_path)
        #     saved_files.append(jsonl_path)
        #     print(f"[✓] JSONL结果保存至 {jsonl_path}")

    return output_df, saved_files


if __name__ == "__main__":
    # 输入文件路径
    input_csv = "/Users/<USER>/code/mobile_agent_eval/data/raw_traces/fornax_raw_trace.csv"
    output_base = "data/extracted_traces/enhanced_agent_trace"

    print(f"📂 输入文件: {input_csv}")

    # 提取数据并保存为多种格式
    df_metrics, saved_files = extract_all_from_csv(
        csv_path=input_csv,
        save_path=output_base,
        output_format='both'  # 同时输出CSV和JSONL
    )

    print(f"\n📊 提取完成，共 {len(df_metrics)} 条记录")
    print("📄 保存的文件:")
    for file_path in saved_files:
        print(f"  - {file_path}")

