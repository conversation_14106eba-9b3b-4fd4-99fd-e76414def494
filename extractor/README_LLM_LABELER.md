# LLM标签系统使用说明

## 功能概述
基于OpenAI API的智能标签系统，为移动应用操作任务自动添加三个维度的标签：
- **步骤分级**: 简单/中等/复杂
- **应用分类**: 电商/社交/娱乐/地图导航/工具/会议/外卖/其他
- **身份验证**: 需要登录/无需登录/需要特殊权限

## 安装依赖

```bash
pip install openai pandas tqdm
```

## 配置API密钥

设置环境变量：
```bash
export OPENAI_API_KEY="your-api-key-here"
```

或者在运行前设置：
```bash
OPENAI_API_KEY="your-api-key-here" python extractor/batch_labeler.py
```

## 使用方法

### 1. 批量处理
```bash
cd /Users/<USER>/code/mobile_agent_eval
python extractor/batch_labeler.py
```

### 2. 单独使用标签分析器
```python
from extractor.llm_labeler import LLMLabeler

labeler = LLMLabeler()
result = labeler.analyze_instruction(
    instruction="上京东中搜索阿迪达斯的运动鞋。",
    action_trace=["click(...)", "type(...)"],
    human_steps=5
)
print(result)
# 输出: {"步骤分级": "中等", "应用分类": "电商", "身份验证": "无需登录"}
```

## 输出文件
- 输入: `data/groudtruth/human_trace_0616_1511.csv`
- 输出: `data/groudtruth/human_trace_0616_1511_labeled.csv`
- 临时文件: `data/groudtruth/temp_labeled_*.csv` (中间保存)

## 标签分类标准

### 步骤分级
- **简单**: 1-3个操作步骤
- **中等**: 4-6个操作步骤
- **复杂**: 7个以上操作步骤

### 应用分类
- **电商**: 京东、淘宝、天猫、拼多多、苏宁
- **社交**: 微信、QQ、微博、钉钉
- **娱乐**: 网易云音乐、QQ音乐、酷狗音乐、优酷、爱奇艺、哔哩哔哩、小红书、抖音
- **地图导航**: 高德地图、百度地图、腾讯地图
- **工具**: 备忘录、日历、计算器、文件管理
- **会议**: 腾讯会议、钉钉会议、ZOOM
- **外卖**: 饿了么、美团外卖
- **其他**: 未明确分类的应用

### 身份验证
- **需要登录**: 涉及个人账户操作、发送消息、购买等
- **无需登录**: 基础搜索、查看公开内容
- **需要特殊权限**: 会议加入、支付操作、系统设置

## 错误处理
- API调用失败时自动重试（最多3次）
- 解析失败时使用备用分析逻辑
- 每10条记录自动保存中间结果
- 详细的错误日志和进度显示

## 注意事项
1. 确保有足够的OpenAI API额度
2. 处理大量数据时建议分批进行
3. 保持网络连接稳定
4. 定期检查中间保存的文件
