from datetime import datetime
import os
import json
from enum import IntEnum
import pandas as pd

# 定义动作类型枚举
class ActionType(IntEnum):
    LONG_POINT = 0
    NO_ACTION = 1
    UNUSED_2 = 2
    TYPE = 3
    DUAL_POINT = 4
    PRESS_BACK = 5
    PRESS_HOME = 6
    PRESS_ENTER = 7
    UNUSED_8 = 8
    UNUSED_9 = 9
    STATUS_TASK_COMPLETE = 10
    STATUS_TASK_IMPOSSIBLE = 11

def is_tap_action(start, end, threshold=0.04):
    dy = start[0] - end[0]
    dx = start[1] - end[1]
    return (dy ** 2 + dx ** 2) ** 0.5 <= threshold

def extract_human_action(step):
    action_type_id = step['result_action_type']
    action_type = ActionType(action_type_id)
    image_w, image_h = step['image_width'], step['image_height']
    touch = json.loads(step['result_touch_yx'])
    lift = json.loads(step['result_lift_yx'])

    def to_bbox(yx):
        y, x = yx
        px = int(x * image_w)
        py = int(y * image_h)
        delta = 10
        return f"<bbox>{px - delta} {py - delta} {px + delta} {py + delta}</bbox>"

    if action_type == ActionType.DUAL_POINT:
        if is_tap_action(touch, lift):
            return f"click(start_box='{to_bbox(lift)}')"
        else:
            return f"drag(start_box='{to_bbox(touch)}', end_box='{to_bbox(lift)}')"
    elif action_type == ActionType.LONG_POINT:
        return f"click(start_box='{to_bbox(lift)}')"
    elif action_type == ActionType.PRESS_BACK:
        return "press_back()"
    elif action_type == ActionType.PRESS_HOME:
        return "press_home()"
    elif action_type == ActionType.PRESS_ENTER:
        return "press_enter()"
    elif action_type == ActionType.TYPE:
        content = step.get("result_action_text", "")
        return f"type(content='{content}')"
    elif action_type == ActionType.STATUS_TASK_COMPLETE:
        return "finished(content='任务完成')"
    elif action_type == ActionType.STATUS_TASK_IMPOSSIBLE:
        return "unfinished(content='任务失败')"
    elif action_type == ActionType.NO_ACTION:
        return "wait(t='')"
    else:
        return "unknown_action()"

def collect_human_traces(root_dir):
    results = []
    trace_id = 1
    for subdir, _, files in os.walk(root_dir):
        for file in files:
            if file.endswith('.json'):
                json_path = os.path.join(subdir, file)
                with open(json_path, 'r') as f:
                    try:
                        steps = json.load(f)
                    except json.JSONDecodeError:
                        continue
                    if not steps:
                        continue
                    episode_id = steps[0]['episode_id']
                    instruction = steps[0]['instruction']
                    actions = [extract_human_action(step) for step in steps]
                    final_image_path = steps[-1]['image_path']
                    results.append({
                        "id": trace_id,
                        "instruction": instruction,
                        "action_trace": actions,
                        "expected_result": f"完成了“{instruction}”这个任务",
                        "human_steps": len(actions),
                        "episode_id": episode_id,
                        "final_image_path": final_image_path
                    })
                    trace_id += 1
    return results

def save_as_jsonl(data, path):
    with open(path, "w", encoding="utf-8") as f:
        for item in data:
            f.write(json.dumps(item, ensure_ascii=False) + "\n")

def save_as_csv(data, path):
    df = pd.DataFrame(data)
    df.to_csv(path, index=False)

if __name__ == "__main__":
    input_dir = "/Users/<USER>/code/mobile_agent_eval/data/CAGUI/CAGUI_agent/domestic"
    timestamp_tag = datetime.now().strftime("%m%d_%H%M")
    output_jsonl = f"./data/groudtruth/human_trace_{timestamp_tag}.jsonl"
    output_csv = f"./data/groudtruth/human_trace_{timestamp_tag}.csv"
    traces = collect_human_traces(input_dir)
    save_as_jsonl(traces, output_jsonl)
    save_as_csv(traces, output_csv)

    print(f"✅ 提取完成，共 {len(traces)} 条记录")
    print(f"📄 JSONL 路径: {output_jsonl}")
    print(f"📄 CSV 路径: {output_csv}")
