"""
配置文件 - OpenAI API和其他设置
"""
import os

# OpenAI API配置
OPENAI_API_KEY = os.getenv('OPENAI_API_KEY', '')  # 从环境变量获取API密钥
OPENAI_MODEL = "gpt-4o-mini"  # 使用性价比较高的模型
OPENAI_MAX_TOKENS = 1000
OPENAI_TEMPERATURE = 0.1  # 较低温度确保输出稳定

# 标签分类定义
STEP_COMPLEXITY = {
    "简单": "1-3个操作步骤",
    "中等": "4-6个操作步骤", 
    "复杂": "7个以上操作步骤"
}

APP_CATEGORIES = {
    "电商": ["京东", "淘宝", "天猫", "拼多多", "苏宁"],
    "社交": ["微信", "QQ", "微博", "钉钉"],
    "娱乐": ["网易云音乐", "QQ音乐", "酷狗音乐", "优酷", "爱奇艺", "哔哩哔哩", "小红书", "抖音"],
    "地图导航": ["高德地图", "百度地图", "腾讯地图"],
    "工具": ["备忘录", "日历", "计算器", "文件管理"],
    "会议": ["腾讯会议", "钉钉会议", "ZOOM"],
    "外卖": ["饿了么", "美团外卖"],
    "其他": []
}

AUTH_REQUIREMENTS = {
    "需要登录": "涉及个人账户、发送消息、购买等操作",
    "无需登录": "基础搜索、查看公开内容",
    "需要特殊权限": "会议加入、支付操作、系统设置"
}

# API请求配置
REQUEST_TIMEOUT = 30
MAX_RETRIES = 3
RETRY_DELAY = 1  # 秒
