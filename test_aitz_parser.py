#!/usr/bin/env python3
# coding=utf-8

import sys
import os
import json
from datetime import datetime

# 添加extractor目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'extractor'))

from AITZ_parser import collect_human_traces, save_as_jsonl, save_as_csv

def test_single_file():
    """测试单个JSON文件的解析"""
    test_file = "data/general/GENERAL-1359994677477286277/GENERAL-1359994677477286277.json"
    
    if not os.path.exists(test_file):
        print(f"❌ 测试文件不存在: {test_file}")
        return
    
    # 读取测试文件
    with open(test_file, 'r', encoding='utf-8') as f:
        steps = json.load(f)
    
    print(f"📄 测试文件: {test_file}")
    print(f"📊 步骤数量: {len(steps)}")
    print(f"🎯 任务指令: {steps[0].get('instruction', 'N/A')}")
    
    # 检查新字段是否存在
    sample_step = steps[0]
    new_fields = ['coat_action_think', 'coat_action_desc', 'coat_action_result', 
                  'coat_screen_desc', 'result_touch_yx', 'result_lift_yx']
    
    print("\n🔍 新字段检查:")
    for field in new_fields:
        value = sample_step.get(field, 'N/A')
        if field in ['result_touch_yx', 'result_lift_yx']:
            try:
                coords = json.loads(value) if isinstance(value, str) else value
                print(f"  {field}: {coords}")
            except:
                print(f"  {field}: {value}")
        else:
            print(f"  {field}: {value[:100]}..." if len(str(value)) > 100 else f"  {field}: {value}")

def test_parser():
    """测试解析器功能"""
    test_dir = "data/general/GENERAL-1359994677477286277"
    
    if not os.path.exists(test_dir):
        print(f"❌ 测试目录不存在: {test_dir}")
        return
    
    print(f"\n🚀 开始测试解析器...")
    traces = collect_human_traces(test_dir)
    
    if not traces:
        print("❌ 没有解析到任何trace")
        return
    
    print(f"✅ 成功解析 {len(traces)} 条trace")
    
    # 检查第一条trace的结构
    trace = traces[0]
    print(f"\n📋 Trace结构检查:")
    print(f"  ID: {trace.get('id')}")
    print(f"  Episode ID: {trace.get('episode_id')}")
    print(f"  指令: {trace.get('instruction')}")
    print(f"  步骤数: {trace.get('human_steps')}")
    print(f"  Expected Result: {trace.get('expected_result')[:100]}...")
    
    # 检查新字段
    print(f"\n🆕 新字段检查:")
    print(f"  coat_action_think 数量: {len(trace.get('coat_action_think', []))}")
    print(f"  coat_action_desc 数量: {len(trace.get('coat_action_desc', []))}")
    print(f"  coat_action_result 数量: {len(trace.get('coat_action_result', []))}")
    print(f"  coat_screen_desc 数量: {len(trace.get('coat_screen_desc', []))}")
    print(f"  result_touch_yx 数量: {len(trace.get('result_touch_yx', []))}")
    print(f"  result_lift_yx 数量: {len(trace.get('result_lift_yx', []))}")
    print(f"  action_details 数量: {len(trace.get('action_details', []))}")
    
    # 检查action_details结构
    if trace.get('action_details'):
        detail = trace['action_details'][0]
        print(f"\n🔗 Action Details 结构示例:")
        for key, value in detail.items():
            if isinstance(value, str) and len(value) > 50:
                print(f"  {key}: {value[:50]}...")
            else:
                print(f"  {key}: {value}")
    
    # 保存测试结果
    timestamp = datetime.now().strftime("%m%d_%H%M")
    test_jsonl = f"test_output_{timestamp}.jsonl"
    test_csv = f"test_output_{timestamp}.csv"
    
    save_as_jsonl(traces, test_jsonl)
    save_as_csv(traces, test_csv)
    
    print(f"\n💾 测试输出文件:")
    print(f"  JSONL: {test_jsonl}")
    print(f"  CSV: {test_csv}")

if __name__ == "__main__":
    print("🧪 AITZ Parser 测试")
    print("=" * 50)
    
    test_single_file()
    test_parser()
    
    print("\n✅ 测试完成!")
