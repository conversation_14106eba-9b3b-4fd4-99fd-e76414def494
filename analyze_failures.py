#!/usr/bin/env python3
"""
分析Su为0的失败记录，使用豆包模型进行详细分析
"""
import dotenv
import pandas as pd
import json
import os
from volcenginesdkarkruntime import Ark
from typing import List, Dict, Any
from loguru import logger

dotenv.load_dotenv()
# 豆包模型配置
DOUBAO_API_KEY = os.getenv("ARK_API_KEY")
DOUBAO_BASE_URL = os.getenv("ARK_API_BASE")
DOUBAO_MODEL = os.getenv("ARK_MODEL")

def load_data():
    """加载相关数据文件"""
    # 加载评估结果
    results_df = pd.read_csv("/Users/<USER>/code/mobile_agent_eval/results/uitars_evaluation_results.csv")
    
    # 加载智能体数据
    agent_df = pd.read_csv("/Users/<USER>/code/mobile_agent_eval/data/extracted_traces/extracted_traces/mobile_use_uitars.csv")
    
    # 加载人类基准数据
    gt_df = pd.read_csv("/Users/<USER>/code/mobile_agent_eval/data/groudtruth/gt.csv")
    
    return results_df, agent_df, gt_df

def get_failed_records(results_df: pd.DataFrame) -> pd.DataFrame:
    """获取Su为0的失败记录"""
    # 查找Su为0的记录
    failed_records = results_df[results_df['Su'] == 0.0].copy()
    logger.info(f"找到 {len(failed_records)} 条Su为0的失败记录")
    return failed_records

def save_markdown_report(analyses: List[Dict[str, Any]], output_path: str):
    """保存分析结果为 Markdown 格式的报告"""
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write("# 失败分析报告\n\n")
        for i, analysis in enumerate(analyses, 1):
            f.write(f"## 失败案例 {i}\n")
            f.write(f"### 任务指令\n{analysis['instruction']}\n\n")
            f.write("### 分析结果\n")
            f.write(analysis['analysis'])
            f.write("\n\n---\n\n")

def analyze_with_doubao(instruction: str, agent_trace: str, agent_summary: str, 
                       human_trace: str, human_steps: int, metrics: Dict[str, float]) -> str:
    """使用豆包模型分析失败原因"""
    
    if not DOUBAO_API_KEY:
        return "错误：未设置DOUBAO_API_KEY环境变量"
    
    client = Ark(
        api_key=DOUBAO_API_KEY,
        base_url=DOUBAO_BASE_URL
    )
    
    prompt = f"""
作为移动智能体评估专家，请仔细分析以下任务失败案例：

**任务指令：** {instruction}

**智能体执行轨迹：**
{agent_trace}

**智能体总结：**
{agent_summary}

**人类基准轨迹：**
{human_trace}

**人类步骤数：** {human_steps}

**评估指标：**
- Su (任务完成度): {metrics.get('Su', 0)}
- PS (过程正确性): {metrics.get('PS', 0)}
- RE (相对效率): {metrics.get('RE', 0)}
- CR (策略一致性): {metrics.get('CR', 0)}

请从以下四个维度进行深入分析：

1. **任务完成度分析 (Su=0的原因)**：
   - 智能体未能完成任务的具体原因
   - 在哪个关键步骤失败
   - 与预期结果的差距

2. **过程正确性分析 (PS)**：
   - 智能体执行步骤中的错误操作
   - 无效或冗余的步骤
   - 逻辑错误或流程偏差

3. **效率对比分析 (RE)**：
   - 智能体步骤数与人类基准的对比
   - 冗余操作的具体表现
   - 效率低下的原因

4. **策略一致性分析 (CR)**：
   - 智能体策略与人类策略的差异
   - 操作路径的偏离程度
   - 策略选择的合理性

**失败根因总结：**
请总结导致任务失败的根本原因，并提出改进建议。

请用中文回答，分析要具体、深入，重点关注失败的技术原因。
"""

    try:
        response = client.chat.completions.create(
            model=DOUBAO_MODEL,
            messages=[
                {"role": "system", "content": "你是一个专业的移动智能体评估分析师，擅长分析智能体任务执行失败的原因。"},
                {"role": "user", "content": prompt}
            ],
            temperature=0.1,
            max_tokens=2000
        )
        
        return response.choices[0].message.content
        
    except Exception as e:
        return f"分析失败：{str(e)}"

def main():
    """主函数"""
    logger.info("开始分析Su为0的失败记录...")
    
    # 加载数据
    results_df, agent_df, gt_df = load_data()
    
    # 获取失败记录
    failed_records = get_failed_records(results_df)
    
    if len(failed_records) == 0:
        logger.info("没有找到Su为0的失败记录")
        return
    
    # 分析每个失败记录
    analyses = []
    cnt = 0
    for idx, row in failed_records.iterrows():
        logger.info(f"正在分析第 {cnt+1} 条失败记录...")
        logger.info(f"任务: {row['instruction']}")
        
        # 获取对应的智能体数据 - 使用instruction关联
        agent_matches = agent_df[agent_df['instruction'] == row['instruction']]
        if len(agent_matches) == 0:
            logger.warning(f"未找到instruction为 '{row['instruction']}' 的智能体记录，跳过该记录")
            continue
        agent_record = agent_matches.iloc[0]

        # 获取对应的人类基准数据 - 使用instruction关联
        gt_matches = gt_df[gt_df['instruction'] == row['instruction']]
        if len(gt_matches) == 0:
            logger.warning(f"未找到instruction为 '{row['instruction']}' 的人类基准记录，跳过该记录")
            continue
        gt_record = gt_matches.iloc[0]
        
        # 准备分析数据
        metrics = {
            'Su': row['Su'],
            'PS': row['PS'], 
            'RE': row['RE'],
            'CR': row['CR']
        }
        
        analysis = analyze_with_doubao(
            instruction=row['instruction'],
            agent_trace=agent_record['actions'],
            agent_summary=agent_record['ai_summaries'],
            human_trace=gt_record['action_trace'],
            human_steps=gt_record['human_steps'],
            metrics=metrics
        )
        
        analyses.append({
            # 'agent_id': row['agent_id'],
            # 'human_id': row['human_id'],
            'instruction': row['instruction'],
            'analysis': analysis
        })

        cnt += 1
        logger.info(f"分析完成")
    
    output_file = "/Users/<USER>/code/mobile_agent_eval/failure_analysis_results.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(analyses, f, ensure_ascii=False, indent=2)
    
    logger.info(f"\n分析完成！结果已保存到: {output_file}")
    
    # 打印分析结果
    # logger.info("\n=== 失败分析结果 ===")
    # for analysis in analyses:
    #     logger.info(f"\n【智能体ID: {analysis['agent_id']}, 人类ID: {analysis['human_id']}】")
    #     logger.info(f"任务: {analysis['instruction']}")
    #     logger.info("分析结果:")
    #     logger.info(analysis['analysis'])
    #     logger.info("-" * 80)
    # 保存分析结果为 Markdown 文件
    markdown_output_file = "/Users/<USER>/code/mobile_agent_eval/failure_analysis_report.md"
    save_markdown_report(analyses, markdown_output_file)
    logger.info(f"Markdown 报告已保存到: {markdown_output_file}")
    

if __name__ == "__main__":
    main()
