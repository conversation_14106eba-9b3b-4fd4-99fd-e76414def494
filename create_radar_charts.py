#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建雷达图对比SingleAgent和MultiAgent的性能
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from math import pi
import matplotlib.font_manager as fm
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def extract_numeric_values(df):
    """提取数值型指标"""
    # 处理RE列 - 提取分数计算结果
    df['RE_numeric'] = df['RE'].apply(lambda x: float(str(x).split('=')[-1].strip()) if '=' in str(x) else float(x))
    
    # 处理CR列 - 提取分数计算结果  
    df['CR_numeric'] = df['CR'].apply(lambda x: float(str(x).split('=')[-1].strip()) if '=' in str(x) else float(x))
    
    # Su和PS已经是数值型
    df['Su_numeric'] = df['Su'].astype(float)
    df['PS_numeric'] = df['PS'].astype(float)
    
    return df

def create_radar_chart(data_dict, title, filename):
    """创建雷达图"""
    # 指标名称
    categories = ['Su (任务完成度)', 'PS (过程正确性)', 'RE (相对效率)', 'CR (策略一致性)']

    # 指标最佳值 (Su=1.0, PS=1.0, RE=1.0, CR=1.0)
    optimal_values = [1.0, 1.0, 1.0, 1.0]

    # 计算角度
    N = len(categories)
    angles = [n / float(N) * 2 * pi for n in range(N)]
    angles += angles[:1]  # 闭合图形

    # 创建图形
    fig, ax = plt.subplots(figsize=(12, 10), subplot_kw=dict(projection='polar'))

    # 设置背景颜色
    fig.patch.set_facecolor('white')
    ax.set_facecolor('#f8f9fa')

    # 绘制最佳值参考线
    optimal_plot = optimal_values + optimal_values[:1]
    ax.plot(angles, optimal_plot, '--', linewidth=3, color='#2ECC71', alpha=0.8, label='最佳值参考线')
    ax.fill(angles, optimal_plot, alpha=0.1, color='#2ECC71')

    # 为每个系统绘制雷达图 - 使用更好看的颜色
    colors = ['#E74C3C', '#3498DB']  # 红色和蓝色
    markers = ['o', 's']
    line_styles = ['-', '-']

    for i, (system_name, values) in enumerate(data_dict.items()):
        values_plot = values + values[:1]  # 闭合图形

        # 绘制线条和填充
        ax.plot(angles, values_plot, line_styles[i], linewidth=3, label=system_name,
                color=colors[i], marker=markers[i], markersize=10, markerfacecolor='white',
                markeredgewidth=2, markeredgecolor=colors[i])
        ax.fill(angles, values_plot, alpha=0.2, color=colors[i])

        # 不在雷达图上显示数值，避免遮挡，数值将在下方表格中显示

    # 设置标签
    ax.set_xticks(angles[:-1])
    ax.set_xticklabels(categories, fontsize=13, fontweight='bold')

    # 设置y轴范围和标签 - 重点突出0.8-1.2区间
    ax.set_ylim(0.75, 1.5)
    ax.set_yticks([0.8, 0.9, 0.95, 1.0, 1.25, 1.4])
    ax.set_yticklabels(['0.8', '0.9','0.95', '1.0', '1.25','1.4'], fontsize=11)
    ax.grid(True, alpha=0.3)

    # 添加标题和图例
    plt.title(title, size=18, fontweight='bold', pad=30, color='#2C3E50')
    plt.legend(loc='upper right', bbox_to_anchor=(1.4, 1.0), fontsize=12,
              frameon=True, fancybox=True, shadow=True)

    # 添加数据表格
    table_data = []
    for system_name, values in data_dict.items():
        row = [system_name] + [f'{v:.3f}' for v in values]
        table_data.append(row)

    # 在图的下方添加表格
    table_headers = ['agent-模型'] + [cat.split('(')[0].strip() for cat in categories]
    table = ax.table(cellText=table_data, colLabels=table_headers,
                    cellLoc='center', loc='bottom', bbox=[0, -0.3, 1, 0.2])
    table.auto_set_font_size(False)
    table.set_fontsize(10)
    table.scale(1, 1.5)

    # 设置表格样式
    for i in range(len(table_headers)):
        table[(0, i)].set_facecolor('#E8F4FD')
        table[(0, i)].set_text_props(weight='bold')

    # 添加说明文字
    plt.figtext(0.02, 0.02, '注：RE(相对效率)越接近1.0越好，其他指标越高越好',
               fontsize=10, style='italic', color='#7F8C8D')

    # 保存图片
    plt.tight_layout()
    plt.savefig(filename, dpi=300, bbox_inches='tight', facecolor='white')
    plt.close()
    print(f"已保存雷达图: {filename}")

def main():
    # 读取数据
    print("正在读取数据文件...")
    thinkingpro_df = pd.read_csv('results/uitars_evaluation_results.csv')
    uitars_df = pd.read_csv('results/multiagent_uitars_results.csv')
    
    # 提取数值型指标
    thinkingpro_df = extract_numeric_values(thinkingpro_df)
    uitars_df = extract_numeric_values(uitars_df)
    
    # 1. 创建总体性能对比雷达图
    print("创建总体性能对比雷达图...")
    
    # 计算总体平均值
    thinkingpro_overall = [
        thinkingpro_df['Su_numeric'].mean(),
        thinkingpro_df['PS_numeric'].mean(), 
        thinkingpro_df['RE_numeric'].mean(),
        thinkingpro_df['CR_numeric'].mean()
    ]
    
    uitars_overall = [
        uitars_df['Su_numeric'].mean(),
        uitars_df['PS_numeric'].mean(),
        uitars_df['RE_numeric'].mean(), 
        uitars_df['CR_numeric'].mean()
    ]
    
    overall_data = {
        'SingleAgent': thinkingpro_overall,
        'MultiAgent': uitars_overall
    }
    
    create_radar_chart(overall_data, '总体性能对比 - SingleAgent vs MultiAgent', 'ui-tars_overall_performance_radar.png')
    
    # 2. 按应用分类创建雷达图
    print("创建按应用分类的性能对比雷达图...")
    
    # 获取所有应用分类
    app_categories = thinkingpro_df['app_category'].unique()
    
    for category in app_categories:
        print(f"处理应用分类: {category}")
        
        # 筛选该分类的数据
        tp_category = thinkingpro_df[thinkingpro_df['app_category'] == category]
        ui_category = uitars_df[uitars_df['app_category'] == category]
        
        if len(tp_category) == 0 or len(ui_category) == 0:
            print(f"跳过 {category}：数据不足")
            continue
            
        # 计算该分类的平均值
        tp_values = [
            tp_category['Su_numeric'].mean(),
            tp_category['PS_numeric'].mean(),
            tp_category['RE_numeric'].mean(),
            tp_category['CR_numeric'].mean()
        ]
        
        ui_values = [
            ui_category['Su_numeric'].mean(),
            ui_category['PS_numeric'].mean(),
            ui_category['RE_numeric'].mean(),
            ui_category['CR_numeric'].mean()
        ]
        
        category_data = {
            'SingleAgent': tp_values,
            'MultiAgent': ui_values
        }
        
        # 创建文件名（替换特殊字符）
        safe_category = category.replace('——', '_').replace('/', '_')
        filename = f'ui-tars_{safe_category}_performance_radar.png'
        title = f'{category} - 性能对比'
        
        create_radar_chart(category_data, title, filename)
    
    print("所有雷达图创建完成！")
    
    # 打印统计信息
    print("\n=== 统计信息 ===")
    print("SingleAgent 总体平均值:")
    print(f"  Su (任务完成度): {thinkingpro_overall[0]:.3f}")
    print(f"  PS (过程正确性): {thinkingpro_overall[1]:.3f}")
    print(f"  RE (相对效率): {thinkingpro_overall[2]:.3f}")
    print(f"  CR (策略一致性): {thinkingpro_overall[3]:.3f}")
    
    print("\nMultiAgent 总体平均值:")
    print(f"  Su (任务完成度): {uitars_overall[0]:.3f}")
    print(f"  PS (过程正确性): {uitars_overall[1]:.3f}")
    print(f"  RE (相对效率): {uitars_overall[2]:.3f}")
    print(f"  CR (策略一致性): {uitars_overall[3]:.3f}")

if __name__ == "__main__":
    main()
